<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Interfaces\RouteCollectorProxyInterface as RouteCollectorProxy;

return function (RouteCollectorProxy $group) {

    // Article list
    $group->get('', function (Request $request, ResponseInterface $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);

        // Get query parameters
        $queryParams = $request->getQueryParams();
        $limit = (int)($queryParams['limit'] ?? 20);
        $offset = (int)($queryParams['offset'] ?? 0);
        $status = $queryParams['status'] ?? null;
        $search = $queryParams['search'] ?? null;

        // Get article list data
        $articleListData = $articleListService->getArticleList($limit, $offset, $status, $search);

        $articlesContent = $renderer->fetch('themes/modern/pages/mark/articles.php', [
            'title' => 'Articles',
            'articles' => $articleListData['articles'],
            'pagination' => $articleListData['pagination'],
            'filters' => $articleListData['filters'] ?? []
        ]);

        return $renderer->render($response, 'themes/modern/layouts/mark.php', [
            'title' => 'Articles',
            'pageTitle' => 'Article Management',
            'currentRoute' => 'mark-articles',
            'content' => $articlesContent
        ]);
    })->setName('mark-articles');

    // Create article form
    $group->get('/create', function (Request $request, ResponseInterface $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        $articleCreateContent = $renderer->fetch('themes/modern/pages/mark/article-create.php', [
            'title' => 'Create Article',
            'formData' => []
        ]);

        return $renderer->render($response, 'themes/modern/layouts/mark.php', [
            'title' => 'Create Article',
            'pageTitle' => 'Create New Article',
            'currentRoute' => 'mark-article-create',
            'content' => $articleCreateContent
        ]);
    })->setName('mark-article-create');

    // Submit article creation
    $group->post('/create', function (Request $request, ResponseInterface $response) {
        $articleCreator = $this->get(\App\Module\Article\Create\Service\ArticleCreator::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);

        try {
            $data = $request->getParsedBody();
            
            $articleId = $articleCreator->createArticle([
                'title' => $data['title'] ?? '',
                'slug' => $data['slug'] ?? '',
                'excerpt' => $data['excerpt'] ?? '',
                'content' => $data['content'] ?? '',
                'image' => $data['image'] ?? null,
                'author_id' => $data['author_id'] ?? null,
                'article_type' => $data['article_type'] ?? 'blog_post',
                'product_subtype' => $data['product_subtype'] ?? null,
                'price' => $data['price'] ?? null,
                'currency' => $data['currency'] ?? 'EUR',
                'status' => $data['status'] ?? 'draft',
                'is_featured' => isset($data['is_featured']),
                'is_public' => isset($data['is_public']),
                'meta_title' => $data['meta_title'] ?? null,
                'meta_description' => $data['meta_description'] ?? null,
                'meta_keywords' => $data['meta_keywords'] ?? null,
            ]);

            return $jsonResponder->success($response, [
                'message' => 'Article created successfully',
                'article_id' => $articleId
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to create article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-create-submit');

    // Edit article form
    $group->get('/{id}/edit', function (Request $request, ResponseInterface $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);
        $articleId = $args['id'];

        try {
            $article = $articleListService->getArticleById($articleId);

            $articleEditContent = $renderer->fetch('themes/modern/pages/mark/article-edit.php', [
                'title' => 'Edit Article',
                'article' => $article,
                'formData' => $article
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Edit Article',
                'pageTitle' => 'Edit: ' . $article['title'],
                'currentRoute' => 'mark-article-edit',
                'content' => $articleEditContent
            ]);

        } catch (\Exception $e) {
            // Redirect to articles list if article not found
            return $response->withHeader('Location', '/mark/articles')->withStatus(302);
        }
    })->setName('mark-article-edit');

    // Submit article update
    $group->put('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $articleUpdater = $this->get(\App\Module\Article\Update\Service\ArticleUpdater::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        $articleId = $args['id'];

        try {
            $data = $request->getParsedBody();
            
            $success = $articleUpdater->updateArticle($articleId, [
                'title' => $data['title'] ?? '',
                'slug' => $data['slug'] ?? '',
                'excerpt' => $data['excerpt'] ?? '',
                'content' => $data['content'] ?? '',
                'image' => $data['image'] ?? null,
                'author_id' => $data['author_id'] ?? null,
                'article_type' => $data['article_type'] ?? 'blog_post',
                'product_subtype' => $data['product_subtype'] ?? null,
                'price' => $data['price'] ?? null,
                'currency' => $data['currency'] ?? 'EUR',
                'status' => $data['status'] ?? 'draft',
                'is_featured' => isset($data['is_featured']),
                'is_public' => isset($data['is_public']),
                'meta_title' => $data['meta_title'] ?? null,
                'meta_description' => $data['meta_description'] ?? null,
                'meta_keywords' => $data['meta_keywords'] ?? null,
            ]);

            return $jsonResponder->success($response, [
                'message' => 'Article updated successfully'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to update article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-update');

    // Delete article
    $group->delete('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $articleDeleter = $this->get(\App\Module\Article\Delete\Service\ArticleDeleter::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        $articleId = $args['id'];

        try {
            $success = $articleDeleter->deleteArticle($articleId);

            return $jsonResponder->success($response, [
                'message' => 'Article deleted successfully'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to delete article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-delete');

    // View article
    $group->get('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);
        $articleId = $args['id'];

        try {
            $article = $articleListService->getArticleById($articleId);

            $articleViewContent = $renderer->fetch('themes/modern/pages/mark/article-view.php', [
                'title' => 'Article Details',
                'article' => $article
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Article Details',
                'pageTitle' => 'Article: ' . $article['title'],
                'currentRoute' => 'mark-article-view',
                'content' => $articleViewContent
            ]);

        } catch (\Exception $e) {
            // Redirect to articles list if article not found
            return $response->withHeader('Location', '/mark/articles')->withStatus(302);
        }
    })->setName('mark-article-view');

    // Bulk operations
    $group->post('/bulk', function (Request $request, ResponseInterface $response) {
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        
        try {
            $data = $request->getParsedBody();
            $action = $data['action'] ?? '';
            $articleIds = $data['article_ids'] ?? [];

            switch ($action) {
                case 'delete':
                    // TODO: Implement bulk delete
                    break;
                case 'publish':
                    // TODO: Implement bulk publish
                    break;
                case 'unpublish':
                    // TODO: Implement bulk unpublish
                    break;
                default:
                    throw new \InvalidArgumentException('Invalid bulk action');
            }

            return $jsonResponder->success($response, [
                'message' => 'Bulk operation completed successfully'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Bulk operation failed: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-articles-bulk');

};
