<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Interfaces\RouteCollectorProxyInterface as RouteCollectorProxy;

return function (RouteCollectorProxy $group) {

    // Article list
    $group->get('', function (Request $request, ResponseInterface $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);

        try {
            // Get query parameters
            $queryParams = $request->getQueryParams();
            $limit = (int)($queryParams['limit'] ?? 20);
            $offset = (int)($queryParams['offset'] ?? 0);
            $status = $queryParams['status'] ?? null;
            $type = $queryParams['type'] ?? null;
            $search = $queryParams['search'] ?? null;

            // Get article list data
            $articleListData = $articleListService->getArticleList($limit, $offset, $status, $type, $search);

            $articlesContent = $renderer->fetch('themes/modern/pages/mark/articles/index.php', [
                'title' => 'Articles',
                'articles' => $articleListData['articles'],
                'pagination' => $articleListData['pagination'],
                'filters' => $articleListData['filters']
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Articles',
                'pageTitle' => 'Article Management',
                'currentRoute' => 'mark-articles',
                'content' => $articlesContent
            ]);

        } catch (\Exception $e) {
            // Fallback to empty list
            $articlesContent = $renderer->fetch('themes/modern/pages/mark/articles/index.php', [
                'title' => 'Articles',
                'articles' => [],
                'pagination' => ['total' => 0],
                'filters' => [],
                'error' => $e->getMessage()
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Articles',
                'pageTitle' => 'Article Management',
                'currentRoute' => 'mark-articles',
                'content' => $articlesContent
            ]);
        }
    })->setName('mark-articles');

    // Create article form
    $group->get('/create', function (Request $request, ResponseInterface $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleCreator = $this->get(\App\Module\Article\Create\Service\ArticleCreator::class);

        try {
            $formData = $articleCreator->getFormData();

            $articleCreateContent = $renderer->fetch('themes/modern/pages/mark/articles/create.php', [
                'title' => 'Create Article',
                'formData' => $formData
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Create Article',
                'pageTitle' => 'Create New Article',
                'currentRoute' => 'mark-article-create',
                'content' => $articleCreateContent
            ]);

        } catch (\Exception $e) {
            // Fallback form
            $articleCreateContent = $renderer->fetch('themes/modern/pages/mark/articles/create.php', [
                'title' => 'Create Article',
                'formData' => [],
                'error' => $e->getMessage()
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Create Article',
                'pageTitle' => 'Create New Article',
                'currentRoute' => 'mark-article-create',
                'content' => $articleCreateContent
            ]);
        }
    })->setName('mark-article-create');

    // Submit article creation
    $group->post('/create', function (Request $request, ResponseInterface $response) {
        $articleCreator = $this->get(\App\Module\Article\Create\Service\ArticleCreator::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);

        try {
            $data = $request->getParsedBody();

            $articleId = $articleCreator->createArticle($data);

            return $jsonResponder->success($response, [
                'message' => 'Article created successfully',
                'article_id' => $articleId,
                'redirect' => '/mark/articles/' . $articleId
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to create article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-create-submit');

    // View article
    $group->get('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);
        $articleId = $args['id'];

        try {
            $article = $articleListService->getArticleByStringId($articleId);

            $articleViewContent = $renderer->fetch('themes/modern/pages/mark/articles/view.php', [
                'title' => 'Article Details',
                'article' => $article
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Article Details',
                'pageTitle' => 'Article: ' . $article['title'],
                'currentRoute' => 'mark-article-view',
                'content' => $articleViewContent
            ]);

        } catch (\Exception $e) {
            // Redirect to articles list if article not found
            return $response->withHeader('Location', '/mark/articles')->withStatus(302);
        }
    })->setName('mark-article-view');

    // Edit article form
    $group->get('/{id}/edit', function (Request $request, ResponseInterface $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);
        $articleCreator = $this->get(\App\Module\Article\Create\Service\ArticleCreator::class);
        $articleId = $args['id'];

        try {
            $article = $articleListService->getArticleByStringId($articleId);
            $formData = $articleCreator->getFormData();

            $articleEditContent = $renderer->fetch('themes/modern/pages/mark/articles/edit.php', [
                'title' => 'Edit Article',
                'article' => $article,
                'formData' => $formData
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Edit Article',
                'pageTitle' => 'Edit: ' . $article['title'],
                'currentRoute' => 'mark-article-edit',
                'content' => $articleEditContent
            ]);

        } catch (\Exception $e) {
            // Redirect to articles list if article not found
            return $response->withHeader('Location', '/mark/articles')->withStatus(302);
        }
    })->setName('mark-article-edit');

    // Submit article update
    $group->put('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $articleUpdater = $this->get(\App\Module\Article\Update\Service\ArticleUpdater::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        $articleId = $args['id'];

        try {
            $data = $request->getParsedBody();

            $success = $articleUpdater->updateArticle($articleId, $data);

            return $jsonResponder->success($response, [
                'message' => 'Article updated successfully'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to update article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-update');

    // Delete article
    $group->delete('/{id}', function (Request $request, ResponseInterface $response, array $args) {
        $articleDeleter = $this->get(\App\Module\Article\Delete\Service\ArticleDeleter::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        $articleId = $args['id'];

        try {
            $success = $articleDeleter->deleteArticle($articleId);

            return $jsonResponder->success($response, [
                'message' => 'Article deleted successfully'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to delete article: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-delete');

    // Bulk operations
    $group->post('/bulk', function (Request $request, ResponseInterface $response) {
        $articleDeleter = $this->get(\App\Module\Article\Delete\Service\ArticleDeleter::class);
        $articleUpdater = $this->get(\App\Module\Article\Update\Service\ArticleUpdater::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);

        try {
            $data = $request->getParsedBody();
            $action = $data['action'] ?? '';
            $articleIds = $data['article_ids'] ?? [];

            switch ($action) {
                case 'delete':
                    $result = $articleDeleter->bulkDelete($articleIds);
                    return $jsonResponder->success($response, [
                        'message' => sprintf('Deleted %d articles', $result['total_deleted']),
                        'result' => $result
                    ]);

                case 'publish':
                    $results = [];
                    foreach ($articleIds as $articleId) {
                        try {
                            $articleUpdater->updateStatus($articleId, 'published');
                            $results[] = $articleId;
                        } catch (\Exception $e) {
                            // Continue with other articles
                        }
                    }
                    return $jsonResponder->success($response, [
                        'message' => sprintf('Published %d articles', count($results))
                    ]);

                case 'unpublish':
                    $results = [];
                    foreach ($articleIds as $articleId) {
                        try {
                            $articleUpdater->updateStatus($articleId, 'draft');
                            $results[] = $articleId;
                        } catch (\Exception $e) {
                            // Continue with other articles
                        }
                    }
                    return $jsonResponder->success($response, [
                        'message' => sprintf('Unpublished %d articles', count($results))
                    ]);

                default:
                    throw new \InvalidArgumentException('Invalid bulk action');
            }

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Bulk operation failed: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-articles-bulk');

    // Quick actions
    $group->post('/{id}/toggle-featured', function (Request $request, ResponseInterface $response, array $args) {
        $articleUpdater = $this->get(\App\Module\Article\Update\Service\ArticleUpdater::class);
        $jsonResponder = $this->get(\App\Infrastructure\Responder\JsonResponder::class);
        $articleId = $args['id'];

        try {
            $success = $articleUpdater->toggleFeatured($articleId);

            return $jsonResponder->success($response, [
                'message' => 'Featured status updated'
            ]);

        } catch (\Exception $e) {
            return $jsonResponder->error($response, [
                'message' => 'Failed to update featured status: ' . $e->getMessage()
            ], 400);
        }
    })->setName('mark-article-toggle-featured');

};
