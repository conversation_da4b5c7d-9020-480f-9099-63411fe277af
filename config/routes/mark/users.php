<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel - User Management Routes
 *
 * Routes for user CRUD operations, roles, permissions, import/export
 */
return function (RouteCollectorProxy $group) use ($jsonResponse) {

    // User list
    $group->get('', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

        // Get query parameters
        $queryParams = $request->getQueryParams();
        $limit = (int)($queryParams['limit'] ?? 20);
        $offset = (int)($queryParams['offset'] ?? 0);

        // Get user list data
        $userListData = $userListService->getUserList($limit, $offset);

        $usersContent = $renderer->fetch('themes/modern/pages/mark/users/index.php', [
            'title' => 'Users',
            'users' => $userListData['users'],
            'pagination' => $userListData['pagination'],
            'filters' => $userListData['filters'] ?? []
        ]);

        return $renderer->render($response, 'themes/modern/layouts/mark.php', [
            'title' => 'Users',
            'pageTitle' => 'User Management',
            'currentRoute' => 'mark-users',
            'content' => $usersContent
        ]);
    })->setName('mark-users');

    // Create user form
    $group->get('/create', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        $userCreateContent = $renderer->fetch('themes/modern/pages/mark/users/create.php', [
            'title' => 'Create User',
            'formData' => []
        ]);

        return $renderer->render($response, 'themes/modern/layouts/mark.php', [
            'title' => 'Create User',
            'pageTitle' => 'Create New User',
            'currentRoute' => 'mark-user-create',
            'content' => $userCreateContent
        ]);
    })->setName('mark-user-create');

    // User roles management (STATIC ROUTES BEFORE VARIABLE ROUTES)
    $group->group('/roles', function (RouteCollectorProxy $roles) use ($jsonResponse) {

        // Roles list
        $roles->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            // Create placeholder content for user roles
            $userRolesContent = '<div class="glass rounded-xl p-12 text-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">User Roles</h3>
                <p class="text-gray-600 dark:text-gray-400">User roles management - implementácia v budúcnosti</p>
            </div>';

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'User Roles',
                'pageTitle' => 'User Roles & Permissions',
                'currentRoute' => 'mark-user-roles',
                'content' => $userRolesContent
            ]);
        })->setName('mark-user-roles');

        // Create role
        $roles->post('', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Role creation not implemented yet'
            ], 501);
        })->setName('mark-user-roles-create');

        // Update role
        $roles->put('/{id}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Role update not implemented yet'
            ], 501);
        })->setName('mark-user-roles-update');

        // Delete role
        $roles->delete('/{id}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Role deletion not implemented yet'
            ], 501);
        })->setName('mark-user-roles-delete');

    });

    // Import/Export (STATIC ROUTES)
    $group->group('/import-export', function (RouteCollectorProxy $importExport) use ($jsonResponse) {

        // Import users
        $importExport->get('/import', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'User import not implemented yet'
            ], 501);
        })->setName('mark-users-import');

        // Export users
        $importExport->get('/export', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'User export not implemented yet'
            ], 501);
        })->setName('mark-users-export');

        // Bulk operations
        $importExport->post('/bulk', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Bulk operations not implemented yet'
            ], 501);
        })->setName('mark-users-bulk');

    });

    // Create user POST
    $group->post('/create', function (Request $request, Response $response) use ($jsonResponse) {
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
        $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);

        try {
            $formData = $request->getParsedBody();

            // Debug logging
            error_log('Create User - Form data: ' . json_encode($formData));

            // Create user
            $userId = $userListService->createUser($formData);

            // Debug logging
            error_log('Create User - Success, User ID: ' . $userId);

            // Log activity
            $activityLogger->logUserCreated($userId, 'admin'); // TODO: Get actual user ID

            // Redirect to user list with success message
            return $response->withHeader('Location', '/mark/users?created=' . $userId)->withStatus(302);

        } catch (\InvalidArgumentException $e) {
            // Debug logging
            error_log('Create User - Validation Error: ' . $e->getMessage());

            // Return to form with error
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            $userCreateContent = $renderer->fetch('themes/modern/pages/mark/users/create.php', [
                'title' => 'Create User',
                'error' => $e->getMessage(),
                'formData' => $formData ?? []
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Create User',
                'pageTitle' => 'Create New User',
                'currentRoute' => 'mark-user-create',
                'content' => $userCreateContent
            ])->withStatus(400);

        } catch (\Exception $e) {
            // Debug logging
            error_log('Create User - General Error: ' . $e->getMessage());
            error_log('Create User - Stack trace: ' . $e->getTraceAsString());

            return $jsonResponse($response, [
                'error' => 'Failed to create user',
                'message' => $e->getMessage()
            ], 500);
        }
    })->setName('mark-user-create-post');

    // View user
    $group->get('/{id}', function (Request $request, Response $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
        $userId = $args['id'];

        try {
            $user = $userListService->getUserById($userId);

            $userViewContent = $renderer->fetch('themes/modern/pages/mark/users/view.php', [
                'title' => 'User Details',
                'user' => $user
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'User Details',
                'pageTitle' => 'User: ' . $user['full_name'],
                'currentRoute' => 'mark-user-view',
                'content' => $userViewContent
            ]);

        } catch (\Exception $e) {
            return $response->withHeader('Location', '/mark/users?error=user_not_found')->withStatus(302);
        }
    })->setName('mark-user-view');

    // Edit user form
    $group->get('/{id}/edit', function (Request $request, Response $response, array $args) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
        $userId = $args['id'];

        try {
            $user = $userListService->getUserById($userId);

            $userEditContent = $renderer->fetch('themes/modern/pages/mark/users/edit.php', [
                'title' => 'Edit User',
                'user' => $user,
                'formData' => $user
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Edit User',
                'pageTitle' => 'Edit User: ' . $user['full_name'],
                'currentRoute' => 'mark-user-edit',
                'content' => $userEditContent
            ]);

        } catch (\Exception $e) {
            return $response->withHeader('Location', '/mark/users?error=user_not_found')->withStatus(302);
        }
    })->setName('mark-user-edit');

    // Update user POST
    $group->post('/{id}/edit', function (Request $request, Response $response, array $args) use ($jsonResponse) {
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
        $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
        $userId = $args['id'];

        try {
            $formData = $request->getParsedBody();

            // Update user
            $success = $userListService->updateUser($userId, $formData);

            if ($success) {
                // Log activity
                $activityLogger->logUserUpdated($userId, 'admin', array_keys($formData)); // TODO: Get actual user ID

                // Redirect to user detail with success message
                return $response->withHeader('Location', '/mark/users/' . $userId . '?updated=1')->withStatus(302);
            } else {
                return $response->withHeader('Location', '/mark/users/' . $userId . '/edit?error=update_failed')->withStatus(302);
            }

        } catch (\InvalidArgumentException $e) {
            // Return to form with error
            $renderer = $this->get(Slim\Views\PhpRenderer::class);
            $user = $userListService->getUserById($userId);

            $userEditContent = $renderer->fetch('themes/modern/pages/mark/users/edit.php', [
                'title' => 'Edit User',
                'user' => $user,
                'error' => $e->getMessage(),
                'formData' => $formData ?? $user
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Edit User',
                'pageTitle' => 'Edit User: ' . $user['full_name'],
                'currentRoute' => 'mark-user-edit',
                'content' => $userEditContent
            ])->withStatus(400);

        } catch (\Exception $e) {
            return $jsonResponse($response, [
                'error' => 'Failed to update user',
                'message' => $e->getMessage()
            ], 500);
        }
    })->setName('mark-user-update');

    // Delete user
    $group->delete('/{id}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
        $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
        $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
        $userId = $args['id'];

        try {
            $success = $userListService->deleteUser($userId);

            if ($success) {
                // Log activity
                $activityLogger->logUserDeleted($userId, 'admin'); // TODO: Get actual user ID

                return $jsonResponse($response, [
                    'success' => true,
                    'message' => 'User deleted successfully',
                    'id' => $userId
                ], 200);
            } else {
                return $jsonResponse($response, [
                    'success' => false,
                    'message' => 'Failed to delete user'
                ], 400);
            }

        } catch (\Exception $e) {
            return $jsonResponse($response, [
                'success' => false,
                'message' => 'Error deleting user: ' . $e->getMessage()
            ], 500);
        }
    })->setName('mark-user-delete');

};
