<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel - API Routes
 *
 * RESTful API endpoints for dashboard, users, content, and system data
 */
return function (RouteCollectorProxy $group) use ($jsonResponse) {

    // Helper method for converting memory limit to bytes
    $convertToBytes = function($value) {
        $unit = strtolower(substr($value, -1));
        $value = (int) $value;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    };

    // Dashboard API
    $group->group('/dashboard', function (RouteCollectorProxy $dashboard) use ($jsonResponse) {

        // Dashboard statistics
        $dashboard->get('/stats', function (Request $request, Response $response) use ($jsonResponse) {
            $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
            $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);

            try {
                // Get user statistics
                $userStats = $userListService->getUserStatistics();

                // Get article statistics
                $articleStats = $articleListService->getArticleStatistics();

                // Get system statistics
                $systemStats = [
                    'totalViews' => '45.2K', // TODO: Implement Analytics
                    'viewsGrowth' => '+8% tento mesiac',
                    'systemStatus' => 'Online',
                    'systemUptime' => '99.9% uptime'
                ];

                // Get recent activity from logs
                $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
                $recentLogs = $activityLogger->getRecentActivity(10);

                $recentActivity = array_map(function($log) {
                    $colorMap = [
                        'user_created' => 'bg-green-500',
                        'user_updated' => 'bg-blue-500',
                        'user_deleted' => 'bg-red-500',
                        'login_success' => 'bg-green-500',
                        'login_failed' => 'bg-red-500',
                        'system_' => 'bg-yellow-500'
                    ];

                    $color = 'bg-gray-500';
                    foreach ($colorMap as $type => $typeColor) {
                        if (strpos($log['type'], $type) === 0) {
                            $color = $typeColor;
                            break;
                        }
                    }

                    return [
                        'type' => $log['type'],
                        'message' => $log['message'],
                        'time' => $log['time_ago'],
                        'color' => $color
                    ];
                }, $recentLogs);

                $dashboardData = array_merge($userStats, $articleStats, $systemStats, [
                    'recentActivity' => $recentActivity,
                    'lastUpdated' => date('Y-m-d H:i:s')
                ]);

                return $jsonResponse($response, $dashboardData, 200);

            } catch (\Exception $e) {
                error_log('Dashboard API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load dashboard data',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-dashboard-stats');

        // Dashboard widgets
        $dashboard->get('/widgets', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'widgets' => [
                    'user_stats' => ['enabled' => true, 'position' => 1],
                    'activity_feed' => ['enabled' => true, 'position' => 2],
                    'system_status' => ['enabled' => true, 'position' => 3],
                    'quick_actions' => ['enabled' => true, 'position' => 4]
                ]
            ], 200);
        })->setName('mark-api-dashboard-widgets');

    });

    // Users API
    $group->group('/users', function (RouteCollectorProxy $users) use ($jsonResponse) {

        // User search
        $users->get('/search', function (Request $request, Response $response) use ($jsonResponse) {
            $queryParams = $request->getQueryParams();
            $query = $queryParams['q'] ?? '';

            return $jsonResponse($response, [
                'message' => 'User search not implemented yet',
                'query' => $query
            ], 501);
        })->setName('mark-api-users-search');

        // User statistics
        $users->get('/stats', function (Request $request, Response $response) use ($jsonResponse) {
            $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

            try {
                $stats = $userListService->getUserStatistics();
                return $jsonResponse($response, $stats, 200);
            } catch (\Exception $e) {
                return $jsonResponse($response, [
                    'error' => 'Failed to load user statistics',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-users-stats');

        // Bulk operations
        $users->post('/bulk', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Bulk user operations not implemented yet'
            ], 501);
        })->setName('mark-api-users-bulk');

    });

    // Activity Logs API
    $group->group('/activity', function (RouteCollectorProxy $activity) use ($jsonResponse) {

        // Activity logs
        $activity->get('/logs', function (Request $request, Response $response) use ($jsonResponse) {
            $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);

            try {
                $queryParams = $request->getQueryParams();
                $limit = (int)($queryParams['limit'] ?? 50);
                $page = (int)($queryParams['page'] ?? 1);

                // Get logs
                $logs = $activityLogger->getRecentActivity($limit);
                $stats = $activityLogger->getActivityStats();

                return $jsonResponse($response, [
                    'logs' => $logs,
                    'total' => $stats['total'],
                    'today' => $stats['today'],
                    'this_week' => $stats['this_week'],
                    'by_type' => $stats['by_type'],
                    'page' => $page,
                    'limit' => $limit
                ], 200);

            } catch (\Exception $e) {
                error_log('Activity Logs API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load activity logs',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-activity-logs');

        // Activity statistics
        $activity->get('/stats', function (Request $request, Response $response) use ($jsonResponse) {
            $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);

            try {
                $stats = $activityLogger->getActivityStats();
                return $jsonResponse($response, $stats, 200);
            } catch (\Exception $e) {
                return $jsonResponse($response, [
                    'error' => 'Failed to load activity statistics',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-activity-stats');

    });

    // System Logs API
    $group->group('/system', function (RouteCollectorProxy $system) use ($jsonResponse) {

        // System logs
        $system->get('/logs', function (Request $request, Response $response) use ($jsonResponse) {
            $systemLogger = $this->get(\App\Infrastructure\Logging\SystemLogger::class);

            try {
                $queryParams = $request->getQueryParams();
                $limit = (int)($queryParams['limit'] ?? 50);
                $level = $queryParams['level'] ?? '';
                $dateFrom = $queryParams['date_from'] ?? '';
                $dateTo = $queryParams['date_to'] ?? '';
                $search = $queryParams['search'] ?? '';

                // Get system logs
                $logs = $systemLogger->getSystemLogs($limit, $level, $dateFrom, $dateTo, $search);
                $stats = $systemLogger->getSystemStats();

                return $jsonResponse($response, [
                    'logs' => $logs,
                    'total' => $stats['total'],
                    'today' => $stats['today'],
                    'this_week' => $stats['this_week'],
                    'by_level' => $stats['by_level'],
                    'by_category' => $stats['by_category']
                ], 200);

            } catch (\Exception $e) {
                error_log('System Logs API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load system logs',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-system-logs');

        // Error logs
        $system->get('/errors', function (Request $request, Response $response) use ($jsonResponse) {
            $systemLogger = $this->get(\App\Infrastructure\Logging\SystemLogger::class);

            try {
                $queryParams = $request->getQueryParams();
                $limit = (int)($queryParams['limit'] ?? 50);
                $severity = $queryParams['severity'] ?? '';
                $dateFrom = $queryParams['date_from'] ?? '';
                $dateTo = $queryParams['date_to'] ?? '';
                $search = $queryParams['search'] ?? '';

                // Get error logs
                $logs = $systemLogger->getErrorLogs($limit, $severity, $dateFrom, $dateTo, $search);
                $stats = $systemLogger->getSystemStats();

                // Count errors by severity
                $errorStats = [
                    'critical' => 0,
                    'error' => 0,
                    'warning' => 0
                ];

                foreach ($stats['by_level'] as $level => $count) {
                    if (in_array($level, ['CRITICAL', 'FATAL'])) {
                        $errorStats['critical'] += $count;
                    } elseif ($level === 'ERROR') {
                        $errorStats['error'] += $count;
                    } elseif ($level === 'WARNING') {
                        $errorStats['warning'] += $count;
                    }
                }

                return $jsonResponse($response, [
                    'logs' => $logs,
                    'total' => count($logs),
                    'today' => $stats['today'],
                    'this_week' => $stats['this_week'],
                    'critical' => $errorStats['critical'],
                    'error' => $errorStats['error'],
                    'warning' => $errorStats['warning']
                ], 200);

            } catch (\Exception $e) {
                error_log('Error Logs API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load error logs',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-system-errors');

        // System information
        $system->get('/info', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'timezone' => date_default_timezone_get(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 200);
        })->setName('mark-api-system-info');

        // System health
        $system->get('/health', function (Request $request, Response $response) use ($jsonResponse) {
            $health = [
                'status' => 'healthy',
                'checks' => [
                    'database' => 'ok',
                    'storage' => 'ok',
                    'memory' => 'ok'
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // Check database connection
            try {
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
                $userListService->getUserStatistics();
            } catch (\Exception $e) {
                $health['checks']['database'] = 'error';
                $health['status'] = 'unhealthy';
            }

            // Check storage
            $logsDir = dirname(__DIR__, 3) . '/logs';
            if (!is_writable($logsDir)) {
                $health['checks']['storage'] = 'error';
                $health['status'] = 'unhealthy';
            }

            // Check memory
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = ini_get('memory_limit');
            if ($memoryLimit !== '-1') {
                // Simple conversion for common units
                $memoryLimitBytes = $memoryLimit;
                if (preg_match('/^(\d+)(.)$/', $memoryLimit, $matches)) {
                    $value = (int)$matches[1];
                    $unit = strtoupper($matches[2]);
                    $memoryLimitBytes = match($unit) {
                        'K' => $value * 1024,
                        'M' => $value * 1024 * 1024,
                        'G' => $value * 1024 * 1024 * 1024,
                        default => $value
                    };
                }
                if ($memoryUsage > $memoryLimitBytes * 0.9) {
                    $health['checks']['memory'] = 'warning';
                }
            }

            $statusCode = $health['status'] === 'healthy' ? 200 : 503;
            return $jsonResponse($response, $health, $statusCode);
        })->setName('mark-api-system-health');

        // System metrics
        $system->get('/metrics', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'memory_limit' => ini_get('memory_limit'),
                'uptime' => time() - $_SERVER['REQUEST_TIME_FLOAT'],
                'load_average' => sys_getloadavg(),
                'disk_free_space' => disk_free_space('.'),
                'disk_total_space' => disk_total_space('.'),
                'timestamp' => date('Y-m-d H:i:s')
            ], 200);
        })->setName('mark-api-system-metrics');

    });

    // Articles API
    $group->group('/articles', function (RouteCollectorProxy $articles) use ($jsonResponse) {

        // Article search
        $articles->get('/search', function (Request $request, Response $response) use ($jsonResponse) {
            $queryParams = $request->getQueryParams();
            $query = $queryParams['q'] ?? '';
            $status = $queryParams['status'] ?? '';

            return $jsonResponse($response, [
                'message' => 'Article search not implemented yet',
                'query' => $query,
                'status' => $status
            ], 501);
        })->setName('mark-api-articles-search');

        // Article statistics
        $articles->get('/stats', function (Request $request, Response $response) use ($jsonResponse) {
            $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);

            try {
                $stats = $articleListService->getArticleStatistics();
                return $jsonResponse($response, $stats, 200);
            } catch (\Exception $e) {
                return $jsonResponse($response, [
                    'error' => 'Failed to load article statistics',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-articles-stats');

        // Recent articles
        $articles->get('/recent', function (Request $request, Response $response) use ($jsonResponse) {
            $articleListService = $this->get(\App\Module\Article\Read\Service\ArticleListService::class);

            try {
                $queryParams = $request->getQueryParams();
                $limit = (int)($queryParams['limit'] ?? 5);

                $recentArticles = $articleListService->getRecentArticles($limit);
                return $jsonResponse($response, [
                    'articles' => $recentArticles
                ], 200);
            } catch (\Exception $e) {
                return $jsonResponse($response, [
                    'error' => 'Failed to load recent articles',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-articles-recent');

        // Bulk operations
        $articles->post('/bulk', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Bulk article operations not implemented yet'
            ], 501);
        })->setName('mark-api-articles-bulk');

    });

    // Content API (placeholder for future implementation)
    $group->group('/content', function (RouteCollectorProxy $content) use ($jsonResponse) {

        // Categories
        $content->get('/categories', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Categories API not implemented yet'
            ], 501);
        })->setName('mark-api-content-categories');

        // Media
        $content->get('/media', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Media API not implemented yet'
            ], 501);
        })->setName('mark-api-content-media');

    });

};
