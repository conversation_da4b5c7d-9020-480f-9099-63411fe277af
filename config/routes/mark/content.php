<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel - Content Management Routes
 *
 * Routes for articles, categories, media files, and other content
 */
return function (RouteCollectorProxy $group) use ($jsonResponse) {

    // Articles management
    $group->get('/articles', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        return $renderer->render($response, 'themes/modern/pages/mark/articles.php', [
            'title' => 'Articles',
            'pageTitle' => 'Article Management',
            'currentRoute' => 'mark-articles'
        ]);
    })->setName('mark-articles');

    // Categories management
    $group->get('/categories', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        return $renderer->render($response, 'themes/modern/pages/mark/categories.php', [
            'title' => 'Categories',
            'pageTitle' => 'Category Management',
            'currentRoute' => 'mark-categories'
        ]);
    })->setName('mark-categories');

    // Media management
    $group->get('/media', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        return $renderer->render($response, 'themes/modern/pages/mark/media.php', [
            'title' => 'Media',
            'pageTitle' => 'Media Library',
            'currentRoute' => 'mark-media'
        ]);
    })->setName('mark-media');

};