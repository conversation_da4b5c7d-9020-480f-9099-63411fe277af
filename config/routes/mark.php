<?php

/**
 * Mark Panel Routes - Refactored Modular Structure
 *
 * Administrative interface routes organized by functional categories:
 *
 * 📊 Dashboard & Analytics - Overview, reports, system monitoring
 * 👥 User Management - CRUD operations, roles, permissions, import/export
 * 📝 Content Management - Articles, categories, media files (future)
 * ⚙️ System Administration - Settings, logs, file management, maintenance
 * 🔌 API Endpoints - RESTful APIs for all functionality
 */

use App\Application\Responder\JsonResponder;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // Helper function for JSON responses
    $jsonResponse = function ($response, $data, $status = 200) use ($app) {
        $container = $app->getContainer();
        if ($container === null) {
            throw new RuntimeException('Container not available');
        }
        $jsonResponder = $container->get(JsonResponder::class);
        if (!$jsonResponder instanceof JsonResponder) {
            throw new RuntimeException('JsonResponder not found in container');
        }

        return $jsonResponder->encodeAndAddToResponse($response, $data, $status);
    };

    // Mark Panel main group
    $app->group('/mark', function (RouteCollectorProxy $mark) use ($jsonResponse) {

        // 📊 Dashboard & Analytics Routes
        // Includes: dashboard overview, analytics, reports, system monitoring
        $mark->group('', require __DIR__ . '/mark/dashboard.php');

        // 👥 User Management Routes
        // Includes: CRUD operations, roles, permissions, import/export, bulk operations
        $mark->group('/users', require __DIR__ . '/mark/users.php');

        // 📰 Article Management Routes
        // Includes: CRUD operations, publishing, SEO, analytics
        $mark->group('/articles', require __DIR__ . '/mark/articles.php');

        // 📝 Content Management Routes
        // Includes: categories, media files, SEO tools
        $mark->group('/content', require __DIR__ . '/mark/content.php');

        // ⚙️ System Administration Routes
        // Includes: settings, logs, file management, system monitoring, maintenance
        $mark->group('', require __DIR__ . '/mark/system.php');

        // 🔌 API Endpoints
        // RESTful APIs for dashboard, users, content, system data
        $mark->group('/api', require __DIR__ . '/mark/api.php');

    });
};