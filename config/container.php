<?php

/**
 * Dependency Injection container configuration.
 *
 * Documentation: https://samuel-gfeller.ch/docs/Dependency-Injection.
 */

use App\Application\Middleware\PhpViewMiddleware;
use App\Infrastructure\Utility\Settings;
use Cake\Database\Connection;
use Cake\Database\Driver\Sqlite;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;
use Nyholm\Psr7\Factory\Psr17Factory;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseFactoryInterface;
use Psr\Http\Message\ServerRequestFactoryInterface;
use Psr\Log\LoggerInterface;
use ResponsiveSk\Slim4Paths\Paths;
use Selective\BasePath\BasePathMiddleware;
use Slim\App;
use Slim\Factory\AppFactory;
use Slim\Interfaces\RouteParserInterface;
use Slim\Views\PhpRenderer;
use SlimErrorRenderer\Middleware\ExceptionHandlingMiddleware;
use SlimErrorRenderer\Middleware\NonFatalErrorHandlingMiddleware;

return [
    // Paths configuration
    Paths::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $root = $settings['root_dir'];

        // ResponsiveSk\Slim4Paths\Paths expects just the base path as string
        return new Paths($root);
    },
    // Make Paths available with 'paths' key for backward compatibility
    'paths' => function (ContainerInterface $container) {
        return $container->get(Paths::class);
    },
    'settings' => function () {
        return require __DIR__ . '/settings.php';
    },

    // Create app instance
    App::class => function (ContainerInterface $container) {
        $app = AppFactory::createFromContainer($container);
        // Register routes
        (require __DIR__ . '/routes.php')($app);

        // Register middlewares
        (require __DIR__ . '/middleware.php')($app);

        return $app;
    },

    // HTTP factories
    ResponseFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },
    ServerRequestFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },

    // Required to create urls with urlFor
    RouteParserInterface::class => function (ContainerInterface $container) {
        return $container->get(App::class)->getRouteCollector()->getRouteParser();
    },

    // Determine the base path in case the application is not running in the root directory
    BasePathMiddleware::class => function (ContainerInterface $container) {
        return new BasePathMiddleware($container->get(App::class));
    },

    // Logging: https://samuel-gfeller.ch/docs/Logging
    LoggerInterface::class => function (ContainerInterface $container) {
        $loggerSettings = $container->get('settings')['logger'];

        $logger = new Logger('app');

        // When testing, 'test' value is true which means the monolog test handler should be used
        if (isset($loggerSettings['test']) && $loggerSettings['test'] === true) {
            return $logger->pushHandler(new Monolog\Handler\TestHandler());
        }

        // Instantiate logger with rotating file handler
        $filename = sprintf('%s/app.log', $loggerSettings['path']);
        $level = $loggerSettings['level'];
        $maxFiles = $loggerSettings['max_files'] ?? 30; // Keep 30 days by default

        // With the RotatingFileHandler, a new log file is created every day
        // maxFiles: 30 means keep last 30 days, older files are automatically deleted
        $rotatingFileHandler = new RotatingFileHandler($filename, $maxFiles, $level, true, 0777);
        // The last "true" here tells monolog to remove empty []'s
        $rotatingFileHandler->setFormatter(new LineFormatter(null, 'Y-m-d H:i:s', false, true));

        return $logger->pushHandler($rotatingFileHandler);
    },

    // Error handling: https://samuel-gfeller.ch/docs/Error-Handling
    ExceptionHandlingMiddleware::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');

        return new ExceptionHandlingMiddleware(
            $container->get(ResponseFactoryInterface::class),
            $settings['error']['log_errors'] ? $container->get(LoggerInterface::class) : null,
            $settings['error']['display_error_details'],
            $settings['public']['main_contact_email'] ?? null
        );
    },

    // Add error middleware for notices and warnings
    NonFatalErrorHandlingMiddleware::class => function (ContainerInterface $container) {
        $settings = $container->get('settings')['error'];

        return new NonFatalErrorHandlingMiddleware(
            $settings['display_error_details'],
            $settings['log_errors'] ? $container->get(LoggerInterface::class) : null,
        );
    },



    // Register repositories with their respective database connections
    App\Domain\User\Repository\UserRepositoryInterface::class => function (ContainerInterface $container) {
        return new App\Infrastructure\Persistence\User\DbUserRepository(
            $container->get('db.connection.user')
        );
    },

    App\Domain\Mark\Repository\MarkRepositoryInterface::class => function (ContainerInterface $container) {
        return new App\Infrastructure\Persistence\Mark\DbMarkRepository(
            $container->get('db.connection.mark')
        );
    },

    App\Module\Article\Domain\Repository\ArticleRepositoryInterface::class => function (ContainerInterface $container) {
        return new App\Module\Article\Infrastructure\Persistence\DbArticleRepository(
            $container->get('db.connection.article')
        );
    },

    App\Module\Article\Read\Service\ArticleListService::class => function (ContainerInterface $container) {
        return new App\Module\Article\Read\Service\ArticleListService(
            $container->get(App\Module\Article\Domain\Repository\ArticleRepositoryInterface::class)
        );
    },

    App\Module\Article\Create\Service\ArticleCreator::class => function (ContainerInterface $container) {
        return new App\Module\Article\Create\Service\ArticleCreator(
            $container->get(App\Module\Article\Domain\Repository\ArticleRepositoryInterface::class)
        );
    },

    App\Module\Article\Update\Service\ArticleUpdater::class => function (ContainerInterface $container) {
        return new App\Module\Article\Update\Service\ArticleUpdater(
            $container->get(App\Module\Article\Domain\Repository\ArticleRepositoryInterface::class)
        );
    },

    App\Module\Article\Delete\Service\ArticleDeleter::class => function (ContainerInterface $container) {
        return new App\Module\Article\Delete\Service\ArticleDeleter(
            $container->get(App\Module\Article\Domain\Repository\ArticleRepositoryInterface::class)
        );
    },



    // User services
    \App\Module\User\Read\Service\UserListService::class => function (ContainerInterface $container) {
        return new \App\Module\User\Read\Service\UserListService(
            new \App\Infrastructure\Persistence\User\DbUserRepository(
                $container->get('db.connection.user')
            )
        );
    },

    // Establish database connection
    Connection::class => function (ContainerInterface $container) {
        $settings = $container->get('settings')['db']['default'];

        // Create SQLite driver instance
        $driver = new Sqlite([
            'database' => $settings['database'] ?? 'var/db/test.sqlite',
        ]);

        return new Connection([
            'driver' => $driver,
        ]);
    },

    // Database connections for each module
    'db.connection.user' => function (ContainerInterface $container) {
        $settings = $container->get('settings');

        // Use database path from settings (now absolute)
        $dbPath = $settings['db']['connections']['user']['database'];

        // Create SQLite driver instance
        $driver = new Sqlite([
            'database' => $dbPath,
        ]);

        return new Connection([
            'driver' => $driver,
        ]);
    },

    'db.connection.article' => function (ContainerInterface $container) {
        $settings = $container->get('settings');

        // Use database path from settings (now absolute)
        $dbPath = $settings['db']['connections']['article']['database'];

        // Create SQLite driver instance
        $driver = new Sqlite([
            'database' => $dbPath,
        ]);

        return new Connection([
            'driver' => $driver,
        ]);
    },

    'db.connection.mark' => function (ContainerInterface $container) {
        $settings = $container->get('settings');

        // Use database path from settings (now absolute)
        $dbPath = $settings['db']['connections']['mark']['database'];

        // Create SQLite driver instance
        $driver = new Sqlite([
            'database' => $dbPath,
        ]);

        return new Connection([
            'driver' => $driver,
        ]);
    },

    // PDO instance for default connection (required for Phinx and testing)
    PDO::class => function (ContainerInterface $container) {
        $driver = $container->get(Connection::class)->getDriver();
        $class = new ReflectionClass($driver);
        $method = $class->getMethod('getPdo');
        $method->setAccessible(true);

        return $method->invoke($driver);
    },

    // PDO instances for each module
    'db.pdo.user' => function (ContainerInterface $container) {
        $connection = $container->get('db.connection.user');
        $driver = $connection->getDriver();
        $class = new ReflectionClass($driver);
        $method = $class->getMethod('getPdo');
        $method->setAccessible(true);

        return $method->invoke($driver);
    },

    'db.pdo.article' => function (ContainerInterface $container) {
        $connection = $container->get('db.connection.article');
        $driver = $connection->getDriver();
        $class = new ReflectionClass($driver);
        $method = $class->getMethod('getPdo');
        $method->setAccessible(true);

        return $method->invoke($driver);
    },

    'db.pdo.mark' => function (ContainerInterface $container) {
        $connection = $container->get('db.connection.mark');
        $driver = $connection->getDriver();
        $class = new ReflectionClass($driver);
        $method = $class->getMethod('getPdo');
        $method->setAccessible(true);

        return $method->invoke($driver);
    },
    // Used by command line to generate `schema.sql` for integration testing
    // Documentation: https://samuel-gfeller.ch/docs/Test-Setup#generating-the-schema-file
    'SqlSchemaGenerator' => function (ContainerInterface $container) {
        return new TestTraits\Console\SqlSchemaGenerator(
            $container->get(PDO::class),
            // Schema output folder
            $container->get('settings')['root_dir'] . '/resources/schema'
        );
    },

    // Settings object that classes can inject to get access to the local configuration
    // Documentation: https://samuel-gfeller.ch/docs/Configuration#using-the-settings-class
    Settings::class => function (ContainerInterface $container) {
        return new Settings($container->get('settings'));
    },

    // Activity Logger with proper path resolution
    \App\Infrastructure\Logging\ActivityLogger::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $logPath = $settings['logger']['path'] . '/activity.log';

        return new \App\Infrastructure\Logging\ActivityLogger($logPath);
    },

    // System Logger with proper path resolution
    \App\Infrastructure\Logging\SystemLogger::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $logBasePath = $settings['logger']['path'];

        $systemLogger = new \App\Infrastructure\Logging\SystemLogger($logBasePath);

        // Initialize system logs for PHP development server
        $systemLogger->initializeSystemLogs();

        return $systemLogger;
    },

    // Security Headers Middleware
    \App\Application\Middleware\SecurityHeadersMiddleware::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $securitySettings = $settings['security'] ?? [];

        return new \App\Application\Middleware\SecurityHeadersMiddleware($securitySettings);
    },

    // Rate Limit Middleware
    \App\Application\Middleware\RateLimitMiddleware::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $rateLimitSettings = $settings['rate_limit'] ?? [];

        return new \App\Application\Middleware\RateLimitMiddleware($rateLimitSettings);
    },

    // Template renderer: https://samuel-gfeller.ch/docs/Template-Rendering
    // Helper function to create SQLite connection
    'createSqliteConnection' => function (array $config) {
        $path = $config['path'];

        // Create database file if it doesn't exist
        $dir = dirname($path);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        if (!file_exists($path)) {
            touch($path);
            chmod($path, 0666);
        }

        $dsn = 'sqlite:' . $path;
        $pdo = new PDO($dsn, null, null, $config['flags'] ?? []);

        // Set SQLite specific options
        if (isset($config['sqlite']['journal_mode'])) {
            $pdo->exec("PRAGMA journal_mode = {$config['sqlite']['journal_mode']}");
        }

        $pdo->exec('PRAGMA foreign_keys = ON');

        return $pdo;
    },



    PhpRenderer::class => function (ContainerInterface $container) {
        $settings = $container->get('settings');
        $rendererSettings = $settings['renderer'];

        /** Global attributes are set in @see PhpViewMiddleware */
        return new PhpRenderer($rendererSettings['path']);
    },
];
