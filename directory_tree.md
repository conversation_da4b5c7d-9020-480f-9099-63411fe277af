.
├── bin
│   ├── console.php
│   ├── init-test-data
│   ├── setup-databases
│   └── test-repositories.php
├── config
│   ├── env
│   │   ├── env.dev.php
│   │   ├── env.example.php
│   │   ├── env.github.php
│   │   ├── env.phinx.php
│   │   ├── env.php
│   │   ├── env.prod.php
│   │   ├── env.scrutinizer.php
│   │   └── env.test.php
│   ├── routes
│   │   ├── mark
│   │   │   ├── api.php
│   │   │   ├── articles.php
│   │   │   ├── content.php
│   │   │   ├── dashboard.php
│   │   │   ├── system.php
│   │   │   └── users.php
│   │   ├── api.php
│   │   ├── article.php
│   │   ├── auth.php
│   │   ├── mark.php
│   │   ├── user.php
│   │   └── web.php
│   ├── bootstrap.php
│   ├── container.php
│   ├── defaults.php
│   ├── functions.php
│   ├── middleware.php
│   ├── routes.php
│   └── settings.php
├── docs
│   ├── architecture
│   │   └── core-domain.md
│   ├── refactoring
│   │   └── duplicity-removal.md
│   ├── architecture.md
│   ├── CHANGELOG-2024-05-27.md
│   ├── FONT-MIGRATION-GILROY-2024-05-27.md
│   ├── logging.md
│   ├── paths.md
│   ├── PATHS.md
│   ├── REFACTORING-CSS-JS-2024-05-27.md
│   └── security.md
├── logs
│   ├── activity.log
│   ├── app-2025-05-27.log
│   ├── debug.log
│   ├── empty
│   ├── error.log
│   └── system.log
├── resources
│   ├── migrations
│   │   ├── article
│   │   │   └── 20250526220000_create_articles_table.php
│   │   ├── mark
│   │   ├── user
│   │   │   └── 20240526195500_create_users_table.php
│   │   ├── 20240412152709_db_change_91933133661952cdd_583c.php
│   │   └── 20240526190000_create_marks_table.php
│   ├── schema
│   │   ├── schema.php
│   │   ├── schema.sql
│   │   └── schema_sqlite.sql
│   ├── seeds
│   │   └── UserSeeder.php
│   └── translations
│       └── doc.md
├── src
│   ├── Application
│   │   ├── Middleware
│   │   │   ├── CorsMiddleware.php
│   │   │   ├── PhpViewMiddleware.php
│   │   │   ├── RateLimitMiddleware.php
│   │   │   ├── SecurityHeadersMiddleware.php
│   │   │   └── ValidationExceptionMiddleware.php
│   │   └── Responder
│   │       ├── JsonResponder.php
│   │       ├── RedirectHandler.php
│   │       └── TemplateRenderer.php
│   ├── Console
│   │   └── Command
│   │       ├── BaseModuleCommand.php
│   │       ├── ModuleCreateCommand.php
│   │       └── RunModuleCommand.php
│   ├── Core
│   │   └── Domain
│   │       ├── Enum
│   │       │   ├── Currency.php
│   │       │   ├── ProductStatus.php
│   │       │   └── ProductType.php
│   │       ├── Repository
│   │       │   ├── AbstractRepository.php
│   │       │   └── RepositoryInterface.php
│   │       └── ValueObject
│   │           ├── EntityId.php
│   │           ├── MarkId.php
│   │           └── UserId.php
│   ├── Domain
│   │   └── Exception
│   │       └── DomainRecordNotFoundException.php
│   ├── Infrastructure
│   │   ├── Factory
│   │   │   └── QueryFactory.php
│   │   ├── Logging
│   │   │   ├── ActivityLogger.php
│   │   │   └── SystemLogger.php
│   │   ├── Persistence
│   │   │   ├── Mark
│   │   │   │   └── DbMarkRepository.php
│   │   │   ├── User
│   │   │   │   └── DbUserRepository.php
│   │   │   └── BaseRepository.php
│   │   └── Utility
│   │       ├── Hydrator.php
│   │       ├── JsImportCacheBuster.php
│   │       └── Settings.php
│   ├── Module
│   │   ├── Article
│   │   │   ├── Create
│   │   │   │   └── Service
│   │   │   │       └── ArticleCreator.php
│   │   │   ├── Delete
│   │   │   │   └── Service
│   │   │   │       └── ArticleDeleter.php
│   │   │   ├── Domain
│   │   │   │   ├── Entity
│   │   │   │   │   └── Article.php
│   │   │   │   ├── Enum
│   │   │   │   │   ├── ArticleStatus.php
│   │   │   │   │   ├── ArticleType.php
│   │   │   │   │   └── DigitalDeliveryType.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── ArticleRepositoryInterface.php
│   │   │   │   └── ValueObject
│   │   │   │       ├── ArticleId.php
│   │   │   │       ├── Price.php
│   │   │   │       └── SKU.php
│   │   │   ├── Infrastructure
│   │   │   │   └── Persistence
│   │   │   │       └── DbArticleRepository.php
│   │   │   ├── Read
│   │   │   │   └── Service
│   │   │   │       └── ArticleListService.php
│   │   │   └── Update
│   │   │       └── Service
│   │   │           └── ArticleUpdater.php
│   │   ├── Home
│   │   │   └── Action
│   │   │       ├── HomePageAction.php
│   │   │       └── RedirectToHomePageAction.php
│   │   ├── Mark
│   │   │   ├── Application
│   │   │   │   ├── Command
│   │   │   │   │   ├── CreateMarkCommand.php
│   │   │   │   │   └── CreateMarkHandler.php
│   │   │   │   └── Query
│   │   │   │       ├── GetMarksHandler.php
│   │   │   │       └── GetMarksQuery.php
│   │   │   ├── Domain
│   │   │   │   ├── Entity
│   │   │   │   │   └── Mark.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── MarkRepositoryInterface.php
│   │   │   │   └── ValueObject
│   │   │   │       └── MarkId.php
│   │   │   ├── Infrastructure
│   │   │   │   └── Http
│   │   │   │       └── Controller
│   │   │   │           └── MarkController.php
│   │   │   ├── README.md
│   │   │   └── routes.neon
│   │   ├── Test
│   │   │   ├── Application
│   │   │   │   ├── Command
│   │   │   │   └── Query
│   │   │   ├── Domain
│   │   │   │   ├── Entity
│   │   │   │   ├── Enum
│   │   │   │   ├── Repository
│   │   │   │   └── ValueObject
│   │   │   ├── Infrastructure
│   │   │   │   ├── Http
│   │   │   │   │   └── Controller
│   │   │   │   └── Persistence
│   │   │   ├── tests
│   │   │   │   ├── Application
│   │   │   │   ├── Domain
│   │   │   │   └── Infrastructure
│   │   │   ├── README.md
│   │   │   ├── routes.neon
│   │   │   └── TestModule.php
│   │   ├── User
│   │   │   ├── Create
│   │   │   │   ├── Action
│   │   │   │   │   └── UserCreateAction.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserCreatorRepository.php
│   │   │   │   └── Service
│   │   │   │       └── UserCreator.php
│   │   │   ├── Data
│   │   │   │   └── UserData.php
│   │   │   ├── Delete
│   │   │   │   ├── Action
│   │   │   │   │   └── UserDeleteAction.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserDeleterRepository.php
│   │   │   │   └── Service
│   │   │   │       └── UserDeleter.php
│   │   │   ├── Domain
│   │   │   │   ├── Entity
│   │   │   │   │   └── User.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserRepositoryInterface.php
│   │   │   │   └── ValueObject
│   │   │   │       └── UserId.php
│   │   │   ├── Infrastructure
│   │   │   │   └── Repository
│   │   │   │       └── UserRepositoryAdapter.php
│   │   │   ├── List
│   │   │   │   ├── Action
│   │   │   │   │   ├── ApiUserFetchListAction.php
│   │   │   │   │   ├── UserFetchListAction.php
│   │   │   │   │   └── UserListPageAction.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserListFinderRepository.php
│   │   │   │   └── Service
│   │   │   │       └── UserListFinder.php
│   │   │   ├── Read
│   │   │   │   ├── Action
│   │   │   │   │   └── UserReadPageAction.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserReadFinderRepository.php
│   │   │   │   └── Service
│   │   │   │       ├── UserListService.php
│   │   │   │       └── UserReadFinder.php
│   │   │   ├── Update
│   │   │   │   ├── Action
│   │   │   │   │   └── UserUpdateAction.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── UserUpdaterRepository.php
│   │   │   │   └── Service
│   │   │   │       └── UserUpdater.php
│   │   │   └── Validation
│   │   │       ├── Repository
│   │   │       │   └── UserValidationExistenceCheckerRepository.php
│   │   │       └── Service
│   │   │           └── UserValidator.php
│   │   └── Validation
│   │       └── Exception
│   │           └── ValidationException.php
│   └── Shared
│       └── Domain
│           ├── Enum
│           │   ├── Currency.php
│           │   ├── ProductStatus.php
│           │   └── ProductType.php
│           ├── Repository
│           │   └── RepositoryInterface.php
│           └── ValueObject
│               └── AbstractUuidId.php
├── templates
│   ├── home
│   │   └── home.html.php
│   ├── layout
│   │   ├── assets.html.php
│   │   ├── footer.html.php
│   │   ├── layout.html.php
│   │   └── navbar.html.php
│   ├── themes
│   │   └── modern
│   │       ├── layouts
│   │       │   ├── base.php
│   │       │   └── mark.php
│   │       ├── pages
│   │       │   ├── debug
│   │       │   │   └── routes.php
│   │       │   ├── mark
│   │       │   │   ├── article-create.php
│   │       │   │   ├── articles.php
│   │       │   │   ├── dashboard.php
│   │       │   │   ├── logs-errors.php
│   │       │   │   ├── logs.php
│   │       │   │   ├── logs-system.php
│   │       │   │   ├── settings.php
│   │       │   │   ├── user-create.php
│   │       │   │   ├── user-edit.php
│   │       │   │   ├── users.php
│   │       │   │   └── user-view.php
│   │       │   ├── home.php
│   │       │   └── login.php
│   │       ├── src
│   │       │   ├── assets
│   │       │   │   ├── fonts
│   │       │   │   │   ├── Gilroy-Black.woff2
│   │       │   │   │   ├── Gilroy-Bold.woff2
│   │       │   │   │   ├── Gilroy-Medium.woff2
│   │       │   │   │   ├── Gilroy-Regular.woff2
│   │       │   │   │   └── Gilroy-SemiBold.woff2
│   │       │   │   └── images
│   │       │   │       ├── icons
│   │       │   │       │   ├── checking.svg
│   │       │   │       │   ├── done.svg
│   │       │   │       │   ├── play.svg
│   │       │   │       │   ├── progress.svg
│   │       │   │       │   ├── telegram.svg
│   │       │   │       │   ├── time-forward.svg
│   │       │   │       │   ├── time.svg
│   │       │   │       │   └── youtube.svg
│   │       │   │       ├── nav
│   │       │   │       │   ├── logo.svg
│   │       │   │       │   └── logo-svgo.svg
│   │       │   │       ├── apple-touch-icon.png
│   │       │   │       ├── checking.svg
│   │       │   │       ├── digital-marketing.jpg
│   │       │   │       ├── done.svg
│   │       │   │       ├── favicon-32x32.png
│   │       │   │       ├── favicon.ico
│   │       │   │       ├── javascript.jpg
│   │       │   │       ├── php82.jpg
│   │       │   │       ├── play.svg
│   │       │   │       ├── progress.svg
│   │       │   │       ├── telegram.svg
│   │       │   │       ├── time-forward.svg
│   │       │   │       ├── time.svg
│   │       │   │       ├── web-dev.jpg
│   │       │   │       ├── welcome.jpg
│   │       │   │       └── youtube.svg
│   │       │   ├── css
│   │       │   │   ├── base
│   │       │   │   │   ├── fonts.css
│   │       │   │   │   └── reset.css
│   │       │   │   ├── components
│   │       │   │   │   ├── buttons.css
│   │       │   │   │   ├── cards.css
│   │       │   │   │   ├── forms.css
│   │       │   │   │   ├── navigation.css
│   │       │   │   │   └── notifications.css
│   │       │   │   ├── utilities
│   │       │   │   │   ├── animations.css
│   │       │   │   │   └── helpers.css
│   │       │   │   └── app.css
│   │       │   └── js
│   │       │       ├── components
│   │       │       │   ├── DemoAnimations.js
│   │       │       │   ├── DevTools.js
│   │       │       │   ├── MarkPanel.js
│   │       │       │   └── TinyMCEEditor.js
│   │       │       ├── core
│   │       │       │   ├── api.js
│   │       │       │   ├── config.js
│   │       │       │   └── utils.js
│   │       │       ├── modules
│   │       │       │   ├── animations.js
│   │       │       │   ├── forms.js
│   │       │       │   ├── notifications.js
│   │       │       │   └── theme.js
│   │       │       ├── pages
│   │       │       │   ├── home.js
│   │       │       │   └── login.js
│   │       │       ├── utils
│   │       │       │   └── template-helpers.js
│   │       │       └── app.js
│   │       ├── .env.example
│   │       ├── font-test.html
│   │       ├── .npmrc
│   │       ├── package.json
│   │       ├── pnpm-lock.yaml
│   │       ├── postcss.config.js
│   │       ├── README.md
│   │       ├── tailwind.config.js
│   │       ├── vite.config.js
│   │       └── vite.plugins.js
│   └── user
│       ├── user-list.html.php
│       └── user-read.html.php
├── tests
│   ├── Fixture
│   │   └── UserFixture.php
│   ├── Modules
│   ├── TestCase
│   │   ├── Home
│   │   │   └── HomePageActionTest.php
│   │   └── User
│   │       ├── Create
│   │       │   ├── UserCreateActionTest.php
│   │       │   └── UserCreateProvider.php
│   │       ├── Delete
│   │       │   └── UserDeleteActionTest.php
│   │       ├── List
│   │       │   ├── ApiUserFetchListActionTest.php
│   │       │   ├── UserFetchListActionTest.php
│   │       │   └── UserListPageActionTest.php
│   │       ├── Read
│   │       │   └── UserReadPageActionTest.php
│   │       └── Update
│   │           ├── UserUpdateActionTest.php
│   │           └── UserUpdateProvider.php
│   ├── Trait
│   │   ├── AppTestTrait.php
│   │   └── SqliteDatabaseTestTrait.php
│   └── docs.md
├── var
│   ├── db
│   │   ├── article
│   │   │   └── migrations
│   │   │       └── 20250127000001_create_articles_table.sql
│   │   ├── mark
│   │   │   ├── database.sqlite
│   │   │   └── test.sqlite
│   │   ├── user
│   │   │   ├── database.sqlite
│   │   │   └── test.sqlite
│   │   ├── article.sqlite
│   │   ├── mark.sqlite
│   │   └── user.sqlite
│   └── rate_limits
├── composer.json
├── composer.lock
├── .cs.php
├── directory_tree.md
├── .env
├── .gitignore
├── .htaccess
├── LICENSE
├── PATHS.md
├── phinx.php
├── phpstan-baseline.neon
├── phpstan.neon
├── phpunit.xml
├── README.md
├── ROUTES.md
├── .scrutinizer.yml
└── test-db-connections.php

168 directories, 272 files
