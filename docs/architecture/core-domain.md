# Core Domain Architecture

## Prehľad

Core Domain poskytuje zdieľané komponenty pre všetky moduly aplikácie. Centralizuje spoločné Value Objects, Enumy a Repository patterns pre konzistentný prístup naprieč celou aplikáciou.

## Štruktúra

```
src/Core/Domain/
├── Enum/
│   ├── Currency.php
│   ├── ProductStatus.php
│   └── ProductType.php
├── Repository/
│   ├── RepositoryInterface.php
│   └── AbstractRepository.php
└── ValueObject/
    ├── EntityId.php
    ├── ArticleId.php
    ├── UserId.php
    └── MarkId.php
```

## Komponenty

### 1. Universal EntityId

**Súbor:** `src/Core/Domain/ValueObject/EntityId.php`

Univerzálny Value Object pre všetky entity ID. Nahradil špecifické ID triedy a poskytuje type safety cez entity type checking.

```php
// Vytvorenie ID pre rôzne entity
$userId = EntityId::forUser('550e8400-e29b-41d4-a716-************');
$articleId = EntityId::forArticle(); // Generuje nové UUID
$markId = EntityId::forMark($existingUuid);

// Type safety
$userId->isForEntity('User'); // true
$userId->isForEntity('Article'); // false

// Equality checking
$sameUser = EntityId::forUser('550e8400-e29b-41d4-a716-************');
$userId->equals($sameUser); // true
```

### 2. Type-Safe ID Wrappers

Pre backward compatibility a lepšiu type safety, každý modul má svoj wrapper:

```php
// src/Core/Domain/ValueObject/UserId.php
final class UserId
{
    private EntityId $entityId;
    
    public static function fromString(string $value): self
    {
        return new self(EntityId::forUser($value));
    }
    
    public function toEntityId(): EntityId
    {
        return $this->entityId;
    }
}
```

**Benefity:**
- **Type Safety:** IDE autocomplete a compile-time checking
- **Backward Compatibility:** Existujúci kód funguje bez zmien
- **Universal Conversion:** Možnosť konverzie na EntityId pre generic operácie

### 3. Zdieľané Enumy

Presunuli sa z `App\Shared\Domain\Enum\` do `App\Core\Domain\Enum\`:

- **Currency** - Podporované meny (EUR, USD, CZK)
- **ProductStatus** - Stavy produktov (draft, published, archived)
- **ProductType** - Typy produktov (article, mark, user)

### 4. Universal Repository Interface

**Súbor:** `src/Core/Domain/Repository/RepositoryInterface.php`

```php
/**
 * @template T The entity type
 */
interface RepositoryInterface
{
    public function findById(EntityId $id): ?object;
    public function save(object $entity): void;
    public function delete(object $entity): void;
    public function count(): int;
    public function findAll(int $limit = 10, int $offset = 0): array;
    public function exists(EntityId $id): bool;
}
```

### 5. Abstract Repository Base Class

**Súbor:** `src/Core/Domain/Repository/AbstractRepository.php`

Poskytuje spoločnú implementáciu pre všetky repozitáre:

```php
abstract class AbstractRepository implements RepositoryInterface
{
    protected CakeConnection $connection;
    protected string $tableName;
    protected string $entityType;

    public function findById(EntityId $id): ?object
    {
        // Automatická type safety validácia
        if (!$id->isForEntity($this->entityType)) {
            throw new \InvalidArgumentException(
                sprintf('Expected %s ID, got %s ID', $this->entityType, $id->getEntityType())
            );
        }
        
        // Spoločná implementácia
    }
    
    // Abstract metódy pre konkrétne repozitáre
    abstract protected function mapToEntity(array $row): object;
    abstract protected function mapToRow(object $entity): array;
}
```

## Migrácia z pôvodných tried

### Pred refaktoringom

```php
// Duplicitné ID triedy
class UserId {
    private string $value;
    // ~40 riadkov duplicitného kódu
}

class ArticleId {
    private string $value;
    // ~40 riadkov duplicitného kódu
}

// Fragmentované enumy
use App\Shared\Domain\Enum\Currency;
use App\Module\Article\Domain\Enum\ArticleType;
```

### Po refaktoringu

```php
// Univerzálne ID s type safety
use App\Core\Domain\ValueObject\EntityId;
use App\Core\Domain\ValueObject\UserId;

// Centralizované enumy
use App\Core\Domain\Enum\Currency;
use App\Core\Domain\Enum\ProductStatus;
```

## Výhody Core Domain

### 1. Konzistentnosť
- Všetky moduly používajú rovnaké patterns
- Jednotný prístup k ID objektom
- Centralizované enumy

### 2. Type Safety
- Compile-time type checking
- Runtime validácia entity typov
- IDE autocomplete support

### 3. Redukcia duplicít
- Odstránených ~120 riadkov duplicitného kódu
- Jeden EntityId namiesto N špecifických ID tried
- Centralizované enumy

### 4. Rozšíriteľnosť
- Jednoduché pridanie nových entity typov
- Konzistentné API pre všetky repozitáre
- Pluggable architecture

### 5. Údržba
- Zmeny v ID logike na jednom mieste
- Centralizovaná validácia
- Jednotné error handling

## Použitie v nových moduloch

### Vytvorenie nového entity ID

```php
// 1. Pridať factory metódu do EntityId
public static function forProduct(string $value = null): self
{
    return $value ? self::fromString($value, 'Product') : self::generate('Product');
}

// 2. Vytvoriť type-safe wrapper
final class ProductId
{
    private EntityId $entityId;
    
    public static function fromString(string $value): self
    {
        return new self(EntityId::forProduct($value));
    }
}

// 3. Použiť v entite
class Product
{
    public function __construct(private ProductId $id) {}
}
```

### Vytvorenie nového repozitára

```php
class DbProductRepository extends AbstractRepository
{
    public function __construct(CakeConnection $connection)
    {
        parent::__construct($connection, 'products', 'Product');
    }
    
    protected function mapToEntity(array $row): Product
    {
        return new Product(
            ProductId::fromString($row['id']),
            $row['name']
        );
    }
    
    protected function mapToRow(Product $product): array
    {
        return [
            'id' => $product->getId()->toRfc4122(),
            'name' => $product->getName(),
        ];
    }
}
```

## Záver

Core Domain poskytuje solídny základ pre škálovateľnú a udržateľnú architektúru. Eliminuje duplicity, zlepšuje type safety a poskytuje konzistentné patterns pre všetky moduly aplikácie.

**Dátum:** 2025-01-27  
**Autor:** Augment Agent  
**Status:** Implementované a otestované
