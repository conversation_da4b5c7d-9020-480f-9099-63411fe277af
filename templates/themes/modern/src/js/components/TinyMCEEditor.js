/**
 * TinyMCE Editor Component
 * Handles rich text editor initialization and configuration
 */
export class TinyMCEEditor {
  constructor() {
    this.defaultConfig = {
      height: 500,
      menubar: true,
      plugins: [
        'advlist autolink lists link image charmap print preview anchor',
        'searchreplace visualblocks code fullscreen',
        'insertdatetime media table paste code help wordcount'
      ],
      toolbar: 'undo redo | formatselect | ' +
        'bold italic backcolor | alignleft aligncenter ' +
        'alignright alignjustify | bullist numlist outdent indent | ' +
        'removeformat | help',
      content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
      skin: 'oxide',
      content_css: 'default'
    }

    this.init()
  }

  init() {
    // Check if TinyMCE is needed on this page
    if (!this.hasTinyMCEElements()) {
      return
    }

    this.loadTinyMCE().then(() => {
      this.initializeEditors()
      console.log('📝 TinyMCE Editor component initialized')
    })
  }

  hasTinyMCEElements() {
    return document.querySelector('textarea[data-editor="tinymce"]') !== null ||
           document.querySelector('#content') !== null
  }

  async loadTinyMCE() {
    // Check if TinyMCE is already loaded
    if (typeof tinymce !== 'undefined') {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script')

      // Try local version first, then CDN as fallback
      const tryLoadScript = (src, isLocal = false) => {
        script.src = src
        script.referrerPolicy = 'origin'

        script.onload = () => {
          console.log(`📝 TinyMCE loaded from ${isLocal ? 'local' : 'CDN'}`)
          resolve()
        }

        script.onerror = () => {
          if (isLocal) {
            console.warn('Local TinyMCE not found, trying CDN...')
            // Try CDN as fallback
            tryLoadScript('https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js', false)
          } else {
            console.warn('Failed to load TinyMCE from CDN, using simple editor')
            // Fallback to simple textarea if both fail
            this.initSimpleEditor()
            resolve()
          }
        }

        document.head.appendChild(script)
      }

      // Try local version first
      tryLoadScript('/assets/vendor/tinymce/js/tinymce/tinymce.min.js', true)
    })
  }

  initializeEditors() {
    // Check if TinyMCE is available
    if (typeof tinymce === 'undefined') {
      console.warn('TinyMCE not available, using simple editor')
      this.initSimpleEditor()
      return
    }

    // Initialize default content editor
    if (document.querySelector('#content')) {
      this.initEditor('#content', this.defaultConfig)
    }

    // Initialize all elements with data-editor="tinymce"
    document.querySelectorAll('textarea[data-editor="tinymce"]').forEach(textarea => {
      const customConfig = this.getCustomConfig(textarea)
      this.initEditor(`#${textarea.id}`, customConfig)
    })
  }

  initSimpleEditor() {
    // Fallback to enhanced textarea with basic formatting
    const textareas = document.querySelectorAll('#content, textarea[data-editor="tinymce"]')

    textareas.forEach(textarea => {
      // Add basic formatting toolbar
      this.addSimpleToolbar(textarea)

      // Add auto-resize functionality
      this.addAutoResize(textarea)

      console.log(`📝 Simple editor initialized for #${textarea.id}`)
    })
  }

  addSimpleToolbar(textarea) {
    // Create simple formatting toolbar
    const toolbar = document.createElement('div')
    toolbar.className = 'simple-editor-toolbar flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-t-lg'

    const buttons = [
      { text: 'B', action: 'bold', title: 'Bold' },
      { text: 'I', action: 'italic', title: 'Italic' },
      { text: 'U', action: 'underline', title: 'Underline' },
      { text: '•', action: 'insertUnorderedList', title: 'Bullet List' },
      { text: '1.', action: 'insertOrderedList', title: 'Numbered List' },
      { text: 'Link', action: 'createLink', title: 'Insert Link' }
    ]

    buttons.forEach(btn => {
      const button = document.createElement('button')
      button.type = 'button'
      button.className = 'px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded'
      button.textContent = btn.text
      button.title = btn.title

      button.addEventListener('click', (e) => {
        e.preventDefault()
        this.executeCommand(btn.action, textarea)
      })

      toolbar.appendChild(button)
    })

    // Insert toolbar before textarea
    textarea.parentNode.insertBefore(toolbar, textarea)

    // Update textarea styling
    textarea.className = textarea.className.replace('rounded-lg', 'rounded-b-lg rounded-t-none')
  }

  addAutoResize(textarea) {
    const resize = () => {
      textarea.style.height = 'auto'
      textarea.style.height = Math.max(textarea.scrollHeight, 200) + 'px'
    }

    textarea.addEventListener('input', resize)
    textarea.addEventListener('change', resize)

    // Initial resize
    setTimeout(resize, 100)
  }

  executeCommand(command, textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = textarea.value.substring(start, end)

    let replacement = selectedText

    switch (command) {
      case 'bold':
        replacement = `**${selectedText}**`
        break
      case 'italic':
        replacement = `*${selectedText}*`
        break
      case 'underline':
        replacement = `<u>${selectedText}</u>`
        break
      case 'insertUnorderedList':
        replacement = selectedText.split('\n').map(line => line.trim() ? `• ${line}` : line).join('\n')
        break
      case 'insertOrderedList':
        replacement = selectedText.split('\n').map((line, i) => line.trim() ? `${i + 1}. ${line}` : line).join('\n')
        break
      case 'createLink':
        const url = prompt('Enter URL:')
        if (url) {
          replacement = `[${selectedText || 'Link text'}](${url})`
        }
        break
    }

    // Replace selected text
    textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end)

    // Update cursor position
    const newPos = start + replacement.length
    textarea.setSelectionRange(newPos, newPos)
    textarea.focus()
  }

  initEditor(selector, config = {}) {
    const finalConfig = {
      selector,
      ...this.defaultConfig,
      ...config,
      setup: (editor) => {
        // Custom setup for each editor
        editor.on('init', () => {
          console.log(`📝 TinyMCE editor initialized for ${selector}`)
        })

        editor.on('change', () => {
          // Auto-save functionality could be added here
          this.handleEditorChange(editor)
        })

        // Call custom setup if provided
        if (config.setup) {
          config.setup(editor)
        }
      }
    }

    tinymce.init(finalConfig)
  }

  getCustomConfig(textarea) {
    const config = {}

    // Read configuration from data attributes
    if (textarea.dataset.height) {
      config.height = parseInt(textarea.dataset.height)
    }

    if (textarea.dataset.plugins) {
      config.plugins = textarea.dataset.plugins.split(',')
    }

    if (textarea.dataset.toolbar) {
      config.toolbar = textarea.dataset.toolbar
    }

    // Simple editor configuration
    if (textarea.dataset.simple === 'true') {
      config.plugins = ['lists', 'link', 'paste']
      config.toolbar = 'bold italic | bullist numlist | link'
      config.menubar = false
    }

    return config
  }

  handleEditorChange(editor) {
    // Trigger custom event for editor changes
    const event = new CustomEvent('tinymce-change', {
      detail: {
        editor: editor,
        content: editor.getContent()
      }
    })

    document.dispatchEvent(event)
  }

  // Public methods for external use
  getEditorContent(selector) {
    const editor = tinymce.get(selector.replace('#', ''))
    return editor ? editor.getContent() : null
  }

  setEditorContent(selector, content) {
    const editor = tinymce.get(selector.replace('#', ''))
    if (editor) {
      editor.setContent(content)
    }
  }

  destroyEditor(selector) {
    const editor = tinymce.get(selector.replace('#', ''))
    if (editor) {
      editor.destroy()
    }
  }
}
