<?php
// Set default variables if not provided
$title = $title ?? 'Fretboard Pattern Visualizer';
$description = $description ?? 'Visualize and learn guitar fretboard patterns';
$currentUrl = $currentUrl ?? '/';
$ogImage = $ogImage ?? '/assets/themes/modern/images/og-image.jpg';

// Navigation
$navigation = [
    ['label' => 'Home', 'url' => '/', 'active' => true],
    ['label' => 'About', 'url' => '/about', 'active' => false],
    ['label' => 'Contact', 'url' => '/contact', 'active' => false],
];

// Start output buffering
ob_start();
?>
<!-- Main content starts here -->

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Animated background -->
    <div class="absolute inset-0 gradient-animated opacity-10"></div>

    <!-- Floating elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/20 rounded-full blur-3xl animate-float"></div>
        <div class="absolute top-3/4 right-1/4 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/2 w-48 h-48 bg-primary-400/30 rounded-full blur-2xl animate-float" style="animation-delay: -1.5s;"></div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h1 class="text-5xl md:text-7xl font-bold mb-6">
                <span class="text-gradient-primary">Slim4</span>
                <br>
                <span class="text-gray-900 dark:text-gray-100">Modern Theme</span>
            </h1>

            <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
                Moderná PHP aplikácia s <strong>GSAP animáciami</strong>, <strong>Tailwind CSS</strong>
                a <strong>Alpine.js</strong> reaktivitou. Postavená na Slim4 frameworku.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="/about" class="btn btn-primary btn-lg group">
                    <span>Zistiť viac</span>
                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>

                <a href="/contact" class="btn btn-ghost btn-lg">
                    Kontaktujte nás
                </a>
            </div>
        </div>

        <!-- Tech stack badges -->
        <div class="mt-16 animate-on-scroll">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Postavené s najlepšími technológiami</p>
            <div class="flex flex-wrap justify-center gap-3">
                <span class="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full text-sm font-medium border border-gray-200 dark:border-gray-700">
                    🚀 Slim4 Framework
                </span>
                <span class="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full text-sm font-medium border border-gray-200 dark:border-gray-700">
                    🎨 Tailwind CSS
                </span>
                <span class="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full text-sm font-medium border border-gray-200 dark:border-gray-700">
                    ✨ GSAP Animations
                </span>
                <span class="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full text-sm font-medium border border-gray-200 dark:border-gray-700">
                    ⚡ Alpine.js
                </span>
                <span class="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full text-sm font-medium border border-gray-200 dark:border-gray-700">
                    📦 pnpm
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Prečo Modern Theme?
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Kombinácia najlepších moderných technológií pre vytvorenie rýchlych,
                krásnych a interaktívnych webových aplikácií.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 stagger-children">
            <!-- Feature 1 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Rýchly výkon
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Optimalizované pre rýchlosť s Vite build systémom a moderným CSS/JS.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Responzívny dizajn
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Perfektne funguje na všetkých zariadeniach - od mobilu po desktop.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Dark/Light Mode
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Automatické prepínanie medzi svetlým a tmavým režimom.
                </p>
            </div>

            <!-- Feature 4 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    GSAP Animácie
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Plynulé a profesionálne animácie pre lepší používateľský zážitok.
                </p>
            </div>

            <!-- Feature 5 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Modulárne témy
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Každá téma má vlastné node_modules a build proces pre maximálnu flexibilitu.
                </p>
            </div>

            <!-- Feature 6 -->
            <div class="card card-hover p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Production Ready
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Pripravené na produkčné nasadenie s optimalizáciami a best practices.
                </p>
            </div>
        </div>
    </div>
</section>

<?php
// End of main content
// The buffer will be output in the layout
?>

<!-- CTA Section -->
<section class="py-20 relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-r from-primary-600 to-accent-600"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h2 class="text-4xl font-bold text-white mb-6">
                Pripravení začať?
            </h2>
            <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
                Vyskúšajte Modern Theme a vytvorte úžasnú webovú aplikáciu s najlepšími technológiami.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold">
                    Kontaktujte nás
                </a>
                <a href="/about" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg font-semibold">
                    Zistiť viac
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Articles Section -->
<section class="py-20 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Najnovšie články
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Prečítajte si naše najnovšie články o hre na gitaru a učte sa nové techniky.
            </p>
        </div>


    </div>
</section>

<!-- Main content ends here -->
<?php
// Store the main content in a variable
$mainContent = ob_get_clean();

// Include the layout which will output the main content in the right place
include __DIR__ . '/../layouts/base.php';
?>
