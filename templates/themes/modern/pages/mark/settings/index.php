<?php
// Settings page for mark layout
$pageTitle = $title ?? 'Settings';
$currentRoute = 'mark-settings';
?>

<!-- Settings -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Settings</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Systémové nastavenia a konfigurácia</p>
        </div>
    </div>

    <!-- Settings Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- General Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">General</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Základné nastavenia aplikácie</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Security</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Bezpečnostné nastavenia</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Email</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">SMTP a email nastavenia</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

        <!-- Database Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Database</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Databázové nastavenia</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

        <!-- API Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">API</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">API kľúče a integrácie</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

        <!-- Backup Settings -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Coming Soon</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Backup</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Zálohovanie a obnovenie</p>
            <div class="flex items-center text-sm text-gray-400">
                <span>Implementácia v budúcnosti</span>
            </div>
        </div>

    </div>

    <!-- System Information -->
    <div class="glass rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Systémové informácie</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">PHP Version</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><?php echo PHP_VERSION; ?></dd>
            </div>
            
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Framework</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">Slim 4</dd>
            </div>
            
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Environment</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        Development
                    </span>
                </dd>
            </div>
            
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Memory Usage</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB
                </dd>
            </div>
            
        </div>
    </div>

</div>

<script>
// Settings JavaScript
console.log('Settings - Initializing...');

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Settings page loaded');
});
</script>
