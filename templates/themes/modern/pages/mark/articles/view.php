<?php
// Article view for mark layout
$pageTitle = $title ?? 'Article Details';
$currentRoute = 'mark-article-view';
$article = $article ?? [];
$error = $error ?? null;
?>

<!-- Article View -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Detail článku</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                <?php echo !empty($article['title']) ? htmlspecialchars($article['title']) : 'Zobrazenie článku'; ?>
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/articles" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Spä<PERSON> na zoznam
            </a>
            <?php if (!empty($article['id'])): ?>
            <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>/edit" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Upraviť
            </a>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($error): ?>
    <!-- Error Message -->
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Chyba pri načítavaní článku</h3>
                <p class="mt-1 text-sm text-red-700 dark:text-red-300"><?php echo htmlspecialchars($error); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (empty($article)): ?>
    <!-- Article Not Found -->
    <div class="glass rounded-xl p-12 text-center">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Článok sa nenašiel</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">Požadovaný článok neexistuje alebo bol zmazaný.</p>
        <a href="/mark/articles" class="btn btn-primary">Späť na zoznam článkov</a>
    </div>
    <?php else: ?>

    <!-- Article Header -->
    <div class="glass rounded-xl p-6">
        <div class="flex items-start space-x-6">
            <?php if (!empty($article['image'])): ?>
            <div class="flex-shrink-0">
                <img src="<?php echo htmlspecialchars($article['image']); ?>" 
                     alt="<?php echo htmlspecialchars($article['title']); ?>"
                     class="w-32 h-32 rounded-lg object-cover">
            </div>
            <?php endif; ?>
            
            <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($article['title']); ?>
                    </h1>
                    <?php if ($article['is_featured'] ?? false): ?>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                        ⭐ Featured
                    </span>
                    <?php endif; ?>
                </div>
                
                <div class="flex items-center space-x-4 mb-4">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-100 text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-800 dark:bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-900/20 dark:text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-400">
                        <?php echo htmlspecialchars($article['type_label'] ?? $article['type']); ?>
                    </span>
                    
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-100 text-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-800 dark:bg-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-900/20 dark:text-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-400">
                        <?php echo htmlspecialchars($article['status_label'] ?? $article['status']); ?>
                    </span>
                    
                    <?php if (!($article['is_public'] ?? true)): ?>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                        🔒 Súkromný
                    </span>
                    <?php endif; ?>
                </div>
                
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p><strong>URL:</strong> <code><?php echo htmlspecialchars($article['slug']); ?></code></p>
                    <p><strong>Vytvorené:</strong> <?php echo date('d.m.Y H:i', strtotime($article['created_at'])); ?></p>
                    <?php if (!empty($article['updated_at'])): ?>
                    <p><strong>Upravené:</strong> <?php echo date('d.m.Y H:i', strtotime($article['updated_at'])); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($article['published_at'])): ?>
                    <p><strong>Publikované:</strong> <?php echo date('d.m.Y H:i', strtotime($article['published_at'])); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Information (if applicable) -->
    <?php if ($article['is_product'] ?? false): ?>
    <div class="glass rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Informácie o produkte</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            
            <?php if (!empty($article['price'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Cena</dt>
                <dd class="mt-1 text-lg font-semibold text-gray-900 dark:text-gray-100">
                    <?php echo htmlspecialchars($article['price']['formatted'] ?? $article['price']['amount'] . ' ' . $article['price']['currency']); ?>
                </dd>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($article['sku'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">SKU</dt>
                <dd class="mt-1 text-sm font-mono text-gray-900 dark:text-gray-100">
                    <?php echo htmlspecialchars($article['sku']); ?>
                </dd>
            </div>
            <?php endif; ?>
            
            <?php if (isset($article['inventory'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Skladové zásoby</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <span class="<?php echo ($article['inventory'] ?? 0) > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'; ?>">
                        <?php echo $article['inventory'] ?? 0; ?> ks
                    </span>
                </dd>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($article['digital_delivery_type'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Typ doručenia</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <?php echo htmlspecialchars($article['digital_delivery_type_label'] ?? $article['digital_delivery_type']); ?>
                </dd>
            </div>
            <?php endif; ?>
            
        </div>
        
        <?php if (!empty($article['digital_file'])): ?>
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Digitálny súbor/odkaz</dt>
            <dd class="text-sm font-mono text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                <?php echo htmlspecialchars($article['digital_file']); ?>
            </dd>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Content -->
    <div class="glass rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Obsah</h3>
        
        <?php if (!empty($article['excerpt'])): ?>
        <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 rounded-r-lg">
            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">Excerpt</h4>
            <p class="text-blue-700 dark:text-blue-300"><?php echo htmlspecialchars($article['excerpt']); ?></p>
        </div>
        <?php endif; ?>
        
        <div class="prose prose-gray dark:prose-invert max-w-none">
            <?php echo nl2br(htmlspecialchars($article['content'] ?? '')); ?>
        </div>
        
        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-4">
                <span><strong>Počet slov:</strong> <?php echo $article['word_count'] ?? str_word_count(strip_tags($article['content'] ?? '')); ?></span>
                <span><strong>Čas čítania:</strong> <?php echo $article['reading_time'] ?? ceil(str_word_count(strip_tags($article['content'] ?? '')) / 200); ?> min</span>
                <?php if (!empty($article['seo_score'])): ?>
                <span><strong>SEO skóre:</strong> 
                    <span class="<?php echo ($article['seo_score'] ?? 0) >= 70 ? 'text-green-600' : (($article['seo_score'] ?? 0) >= 50 ? 'text-yellow-600' : 'text-red-600'); ?>">
                        <?php echo $article['seo_score'] ?? 0; ?>/100
                    </span>
                </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- SEO Information -->
    <?php if (!empty($article['meta_title']) || !empty($article['meta_description']) || !empty($article['meta_keywords'])): ?>
    <div class="glass rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">SEO informácie</h3>
        <dl class="space-y-4">
            
            <?php if (!empty($article['meta_title'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Title</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><?php echo htmlspecialchars($article['meta_title']); ?></dd>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($article['meta_description'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Description</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><?php echo htmlspecialchars($article['meta_description']); ?></dd>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($article['meta_keywords'])): ?>
            <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Keywords</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><?php echo htmlspecialchars($article['meta_keywords']); ?></dd>
            </div>
            <?php endif; ?>
            
        </dl>
    </div>
    <?php endif; ?>

    <!-- Actions -->
    <div class="glass rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Akcie</h3>
        <div class="flex flex-wrap gap-3">
            
            <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>/edit" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Upraviť článok
            </a>
            
            <button onclick="toggleFeatured('<?php echo htmlspecialchars($article['id']); ?>')" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="<?php echo $article['is_featured'] ? 'currentColor' : 'none'; ?>" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                <?php echo $article['is_featured'] ? 'Zrušiť featured' : 'Označiť ako featured'; ?>
            </button>
            
            <?php if (($article['status'] ?? '') !== 'published'): ?>
            <button onclick="publishArticle('<?php echo htmlspecialchars($article['id']); ?>')" class="btn btn-success">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Publikovať
            </button>
            <?php endif; ?>
            
            <button onclick="deleteArticle('<?php echo htmlspecialchars($article['id']); ?>')" class="btn btn-danger">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Zmazať článok
            </button>
            
        </div>
    </div>

    <?php endif; ?>

</div>

<script>
// Article view JavaScript
console.log('Article View - Initializing...');

async function toggleFeatured(articleId) {
    try {
        const response = await fetch(`/mark/articles/${articleId}/toggle-featured`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            window.location.reload();
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Toggle featured error:', error);
        alert('Nastala chyba pri zmene featured statusu');
    }
}

async function publishArticle(articleId) {
    if (!confirm('Naozaj chcete publikovať tento článok?')) {
        return;
    }
    
    try {
        const response = await fetch(`/mark/articles/${articleId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'published'
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            window.location.reload();
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Publish error:', error);
        alert('Nastala chyba pri publikovaní článku');
    }
}

async function deleteArticle(articleId) {
    if (!confirm('Naozaj chcete zmazať tento článok? Táto akcia sa nedá vrátiť späť.')) {
        return;
    }
    
    try {
        const response = await fetch(`/mark/articles/${articleId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert(result.message);
            window.location.href = '/mark/articles';
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Delete error:', error);
        alert('Nastala chyba pri mazaní článku');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Article view loaded');
});
</script>
