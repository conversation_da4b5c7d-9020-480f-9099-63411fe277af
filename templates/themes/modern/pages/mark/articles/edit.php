<?php
// Article edit form for mark layout
$pageTitle = $title ?? 'Edit Article';
$currentRoute = 'mark-article-edit';
$article = $article ?? [];
$formData = $formData ?? [];
$error = $error ?? null;
?>

<!-- Article Edit Form -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Upraviť článok</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                <?php echo !empty($article['title']) ? htmlspecialchars($article['title']) : 'Úprava článku'; ?>
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/articles" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Späť na zoznam
            </a>
            <?php if (!empty($article['id'])): ?>
            <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Zobraziť
            </a>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($error): ?>
    <!-- Error Message -->
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Chyba pri načítavaní článku</h3>
                <p class="mt-1 text-sm text-red-700 dark:text-red-300"><?php echo htmlspecialchars($error); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (empty($article)): ?>
    <!-- Article Not Found -->
    <div class="glass rounded-xl p-12 text-center">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Článok sa nenašiel</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">Požadovaný článok neexistuje alebo bol zmazaný.</p>
        <a href="/mark/articles" class="btn btn-primary">Späť na zoznam článkov</a>
    </div>
    <?php else: ?>

    <!-- Article Edit Form -->
    <form id="articleEditForm" class="space-y-6">
        <input type="hidden" name="article_id" value="<?php echo htmlspecialchars($article['id'] ?? ''); ?>">
        
        <!-- Article Type Display -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Typ článku</h3>
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-100 dark:bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-600 dark:text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($article['type_label'] ?? $article['type'] ?? 'Neznámy typ'); ?>
                    </h4>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Typ článku sa nedá zmeniť po vytvorení</p>
                </div>
            </div>
            <input type="hidden" name="article_type" value="<?php echo htmlspecialchars($article['type'] ?? ''); ?>">
        </div>

        <!-- Basic Information -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Základné informácie</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Title -->
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Názov článku *
                    </label>
                    <input type="text" id="title" name="title" required
                           value="<?php echo htmlspecialchars($article['title'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        URL slug
                    </label>
                    <input type="text" id="slug" name="slug"
                           value="<?php echo htmlspecialchars($article['slug'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Stav článku
                    </label>
                    <select id="status" name="status" 
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                        <?php if (!empty($formData['article_statuses'])): ?>
                            <?php foreach ($formData['article_statuses'] as $status): ?>
                            <option value="<?php echo htmlspecialchars($status['value']); ?>" 
                                    <?php echo ($article['status'] ?? '') === $status['value'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($status['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Excerpt -->
                <div class="lg:col-span-2">
                    <label for="excerpt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Krátky popis (excerpt)
                    </label>
                    <textarea id="excerpt" name="excerpt" rows="3"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"><?php echo htmlspecialchars($article['excerpt'] ?? ''); ?></textarea>
                </div>

                <!-- Image -->
                <div class="lg:col-span-2">
                    <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Obrázok
                    </label>
                    <input type="url" id="image" name="image"
                           value="<?php echo htmlspecialchars($article['image'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="https://example.com/image.jpg">
                </div>

            </div>
        </div>

        <!-- Product Information (if applicable) -->
        <?php if ($article['is_product'] ?? false): ?>
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Informácie o produkte</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Price -->
                <?php if (!empty($article['price'])): ?>
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Cena *
                    </label>
                    <div class="flex">
                        <input type="number" id="price" name="price" step="0.01" min="0"
                               value="<?php echo htmlspecialchars($article['price']['amount'] ?? ''); ?>"
                               class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <select id="currency" name="currency" 
                                class="px-4 py-2 border-l-0 border-gray-300 dark:border-gray-600 rounded-r-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                            <option value="EUR" <?php echo ($article['price']['currency'] ?? '') === 'EUR' ? 'selected' : ''; ?>>EUR</option>
                            <option value="USD" <?php echo ($article['price']['currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>USD</option>
                            <option value="CZK" <?php echo ($article['price']['currency'] ?? '') === 'CZK' ? 'selected' : ''; ?>>CZK</option>
                        </select>
                    </div>
                </div>
                <?php endif; ?>

                <!-- SKU -->
                <?php if (!empty($article['sku'])): ?>
                <div>
                    <label for="sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        SKU
                    </label>
                    <input type="text" id="sku" name="sku"
                           value="<?php echo htmlspecialchars($article['sku'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
                <?php endif; ?>

                <!-- Inventory (for physical products) -->
                <?php if (isset($article['inventory'])): ?>
                <div>
                    <label for="inventory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Skladové zásoby
                    </label>
                    <input type="number" id="inventory" name="inventory" min="0"
                           value="<?php echo htmlspecialchars($article['inventory'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
                <?php endif; ?>

                <!-- Digital Delivery (for digital products) -->
                <?php if (!empty($article['digital_delivery_type'])): ?>
                <div>
                    <label for="digital_delivery_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Typ doručenia
                    </label>
                    <select id="digital_delivery_type" name="digital_delivery_type" 
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                        <?php if (!empty($formData['digital_delivery_types'])): ?>
                            <?php foreach ($formData['digital_delivery_types'] as $deliveryType): ?>
                            <option value="<?php echo htmlspecialchars($deliveryType['value']); ?>" 
                                    <?php echo ($article['digital_delivery_type'] ?? '') === $deliveryType['value'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($deliveryType['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Digital File -->
                <div>
                    <label for="digital_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Súbor/Odkaz
                    </label>
                    <input type="text" id="digital_file" name="digital_file"
                           value="<?php echo htmlspecialchars($article['digital_file'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
                <?php endif; ?>

            </div>
        </div>
        <?php endif; ?>

        <!-- Content -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Obsah článku</h3>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Obsah *
                </label>
                <textarea id="content" name="content" rows="15" required
                          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"><?php echo htmlspecialchars($article['content'] ?? ''); ?></textarea>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">SEO nastavenia</h3>
            <div class="space-y-4">
                
                <!-- Meta Title -->
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Meta Title
                    </label>
                    <input type="text" id="meta_title" name="meta_title" maxlength="60"
                           value="<?php echo htmlspecialchars($article['meta_title'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>

                <!-- Meta Description -->
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Meta Description
                    </label>
                    <textarea id="meta_description" name="meta_description" rows="3" maxlength="160"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"><?php echo htmlspecialchars($article['meta_description'] ?? ''); ?></textarea>
                </div>

                <!-- Meta Keywords -->
                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Kľúčové slová
                    </label>
                    <input type="text" id="meta_keywords" name="meta_keywords"
                           value="<?php echo htmlspecialchars($article['meta_keywords'] ?? ''); ?>"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>

            </div>
        </div>

        <!-- Publishing Options -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Možnosti publikovania</h3>
            <div class="space-y-4">
                
                <!-- Featured -->
                <div class="flex items-center">
                    <input type="checkbox" id="is_featured" name="is_featured" value="1"
                           <?php echo ($article['is_featured'] ?? false) ? 'checked' : ''; ?>
                           class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500">
                    <label for="is_featured" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Označiť ako odporúčaný článok
                    </label>
                </div>

                <!-- Public -->
                <div class="flex items-center">
                    <input type="checkbox" id="is_public" name="is_public" value="1"
                           <?php echo ($article['is_public'] ?? true) ? 'checked' : ''; ?>
                           class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500">
                    <label for="is_public" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Verejne dostupný
                    </label>
                </div>

            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                * Povinné polia
            </div>
            <div class="flex space-x-3">
                <button type="button" onclick="saveDraft()" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Uložiť koncept
                </button>
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Uložiť zmeny
                </button>
            </div>
        </div>

    </form>

    <?php endif; ?>

</div>

<script>
// Article edit form JavaScript
console.log('Article Edit - Initializing...');

const form = document.getElementById('articleEditForm');
const articleId = document.querySelector('input[name="article_id"]')?.value;

if (form && articleId) {
    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        await submitForm('update');
    });

    function saveDraft() {
        // Set status to draft and submit
        document.getElementById('status').value = 'draft';
        submitForm('draft');
    }

    async function submitForm(action) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Add action type
        data.action = action;

        try {
            const response = await fetch(`/mark/articles/${articleId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                alert(result.message);
                // Stay on edit page or redirect to view
                if (action === 'update') {
                    window.location.href = `/mark/articles/${articleId}`;
                }
            } else {
                alert('Chyba: ' + result.message);
            }
        } catch (error) {
            console.error('Submit error:', error);
            alert('Nastala chyba pri ukladaní článku');
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Article edit form loaded');
});
</script>
