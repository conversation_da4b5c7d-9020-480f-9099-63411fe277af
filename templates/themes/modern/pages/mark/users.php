<?php
// Users content will be included in mark layout
$pageTitle = $title ?? 'User Management';
$currentRoute = 'mark-users';
?>

<!-- User Management -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Správa používateľov</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Spravujte používateľské účty a oprávnenia</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <!-- Test notification button -->
            <button onclick="testNotification()" class="btn btn-secondary">
                🔔 Test notifikácie
            </button>
            <!-- Test CRUD notifications -->
            <button onclick="testCrudNotifications()" class="btn btn-secondary">
                🔧 Test CRUD
            </button>
            <a href="/mark/users/create" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Nový používateľ
            </a>
        </div>
    </div>



    <!-- Filters -->
    <div class="glass rounded-xl p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hľadať</label>
                <input type="text" placeholder="Meno, email..." class="form-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rola</label>
                <select class="form-select">
                    <option value="">Všetky role</option>
                    <option value="admin">Admin</option>
                    <option value="editor">Editor</option>
                    <option value="user">Používateľ</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stav</label>
                <select class="form-select">
                    <option value="">Všetky stavy</option>
                    <option value="active">Aktívny</option>
                    <option value="inactive">Neaktívny</option>
                    <option value="banned">Zablokovaný</option>
                </select>
            </div>
            <div class="flex items-end">
                <button class="btn btn-secondary w-full">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrovať
                </button>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Používateľ
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Rola
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Stav
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Posledná aktivita
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Akcie
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php if (!empty($users)): ?>
                        <?php foreach ($users as $user): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium text-sm"><?php echo htmlspecialchars($user['initials'], ENT_QUOTES, 'UTF-8'); ?></span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username'], ENT_QUOTES, 'UTF-8'); ?>
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo htmlspecialchars($user['email'], ENT_QUOTES, 'UTF-8'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-<?php echo $user['role_color']; ?>-100 dark:bg-<?php echo $user['role_color']; ?>-900/20 text-<?php echo $user['role_color']; ?>-800 dark:text-<?php echo $user['role_color']; ?>-300">
                                        <?php echo htmlspecialchars($user['role_label'], ENT_QUOTES, 'UTF-8'); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-<?php echo $user['status_color']; ?>-100 dark:bg-<?php echo $user['status_color']; ?>-900/20 text-<?php echo $user['status_color']; ?>-800 dark:text-<?php echo $user['status_color']; ?>-300">
                                        <?php echo htmlspecialchars($user['status_label'], ENT_QUOTES, 'UTF-8'); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo htmlspecialchars($user['last_activity'], ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="/mark/users/<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>"
                                           class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                                           title="Zobraziť detaily">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        <a href="/mark/users/<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>/edit"
                                           class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300"
                                           title="Upraviť používateľa">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <button onclick="deleteUser('<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($user['full_name'] ?: $user['username'], ENT_QUOTES, 'UTF-8'); ?>')"
                                                class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                                                title="Zmazať používateľa">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="text-gray-500 dark:text-gray-400">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Žiadni používatelia</h3>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Začnite vytvorením nového používateľa.</p>
                                    <div class="mt-6">
                                        <a href="/mark/users/create" class="btn btn-primary">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Nový používateľ
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if (isset($pagination) && $pagination['total'] > 0): ?>
        <div class="bg-white dark:bg-gray-900 px-6 py-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje <span class="font-medium"><?php echo $pagination['offset'] + 1; ?></span>
                    až <span class="font-medium"><?php echo min($pagination['offset'] + $pagination['limit'], $pagination['total']); ?></span>
                    z <span class="font-medium"><?php echo $pagination['total']; ?></span> výsledkov
                </div>
                <div class="flex items-center space-x-2">
                    <?php if ($pagination['has_prev']): ?>
                        <a href="?page=<?php echo $pagination['current_page'] - 1; ?>"
                           class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600">
                            Predchádzajúca
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                        <?php if ($i === $pagination['current_page']): ?>
                            <span class="px-3 py-1 text-sm bg-primary-600 text-white rounded"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?page=<?php echo $i; ?>"
                               class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600">
                                <?php echo $i; ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php if ($pagination['has_next']): ?>
                        <a href="?page=<?php echo $pagination['current_page'] + 1; ?>"
                           class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600">
                            Ďalšia
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

</div>

<!-- Delete User Modal -->
<div id="deleteUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
                <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mt-2">Zmazať používateľa</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Naozaj chcete zmazať používateľa <span id="deleteUserName" class="font-medium"></span>?
                    Táto akcia sa nedá vrátiť späť.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                    Zmazať
                </button>
                <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-base font-medium rounded-md w-24 hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Zrušiť
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Delete user functionality
let userToDelete = null;

function deleteUser(userId, userName) {
    userToDelete = userId;
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteUserModal').classList.remove('hidden');
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (userToDelete) {
        // Send DELETE request
        fetch(`/mark/users/${userToDelete}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Hide modal
            document.getElementById('deleteUserModal').classList.add('hidden');

            // Show notification
            if (window.showNotification) {
                window.showNotification('Používateľ bol úspešne zmazaný', 'success');
            }

            // Reload page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide modal
            document.getElementById('deleteUserModal').classList.add('hidden');

            // Show error notification
            if (window.showNotification) {
                window.showNotification('Chyba pri mazaní používateľa', 'error');
            }
        });
    }
});

document.getElementById('cancelDeleteBtn').addEventListener('click', function() {
    document.getElementById('deleteUserModal').classList.add('hidden');
    userToDelete = null;
});

// Close modal when clicking outside
document.getElementById('deleteUserModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
        userToDelete = null;
    }
});

// Show notifications based on URL parameters
document.addEventListener('DOMContentLoaded', function() {
    // Wait for showNotification to be available
    const checkAndShowNotifications = () => {
        const urlParams = new URLSearchParams(window.location.search);

        console.log('CRUD Notifications - Current URL:', window.location.href);
        console.log('CRUD Notifications - URL params:', urlParams.toString());
        console.log('CRUD Notifications - Has created:', urlParams.has('created'));
        console.log('CRUD Notifications - Has error:', urlParams.has('error'));
        console.log('CRUD Notifications - showNotification available:', typeof window.showNotification);

        // Check if we need to show notifications
        const hasNotifications = urlParams.has('created') || urlParams.has('error');

        if (hasNotifications && !window.showNotification) {
            console.log('CRUD Notifications - showNotification not ready, retrying...');
            setTimeout(checkAndShowNotifications, 500);
            return;
        }

        // Success notifications
        if (urlParams.has('created')) {
            console.log('CRUD Notifications - Showing created notification');
            window.showNotification('Používateľ bol úspešne vytvorený!', 'success');
            console.log('CRUD Notifications - Created notification triggered');
        }

        // Error notifications
        if (urlParams.has('error')) {
            const error = urlParams.get('error');
            console.log('CRUD Notifications - Showing error notification:', error);
            let message = 'Nastala chyba';

            switch(error) {
                case 'user_not_found':
                    message = 'Používateľ nebol nájdený';
                    break;
                default:
                    message = 'Nastala neočakávaná chyba';
            }

            window.showNotification(message, 'error');
            console.log('CRUD Notifications - Error notification triggered');
        }

        // Clean URL after notifications are shown
        if (hasNotifications) {
            setTimeout(() => {
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
                console.log('CRUD Notifications - URL cleaned');
            }, 1000);
        }

        console.log('CRUD Notifications - Setup complete');
    };

    // Start checking immediately
    checkAndShowNotifications();
});

// Test notification function
function testNotification() {
    console.log('Testing notification...');
    console.log('showNotification available:', typeof window.showNotification);

    if (window.showNotification) {
        window.showNotification('Test notifikácia funguje!', 'success');
        setTimeout(() => {
            window.showNotification('Test error notifikácia', 'error');
        }, 1000);
        setTimeout(() => {
            window.showNotification('Test info notifikácia', 'info');
        }, 2000);
    } else {
        alert('showNotification nie je dostupné!');
    }
}

// Test CRUD notifications
function testCrudNotifications() {
    console.log('Testing CRUD notifications...');

    // Test created notification
    const testUrls = [
        '/mark/users?created=123',
        '/mark/users?error=user_not_found',
        '/mark/users?updated=1'
    ];

    testUrls.forEach((url, index) => {
        setTimeout(() => {
            console.log(`Testing URL: ${url}`);

            // Simulate URL change
            window.history.pushState({}, '', url);

            // Trigger URL parameter check
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.has('created')) {
                window.showNotification('Používateľ bol úspešne vytvorený!', 'success');
            } else if (urlParams.has('error')) {
                const error = urlParams.get('error');
                const message = error === 'user_not_found' ? 'Používateľ nebol nájdený' : 'Nastala chyba';
                window.showNotification(message, 'error');
            } else if (urlParams.has('updated')) {
                window.showNotification('Používateľ bol úspešne aktualizovaný!', 'success');
            }

            // Clean URL after test
            setTimeout(() => {
                window.history.replaceState({}, '', '/mark/users');
            }, 2000);

        }, index * 3000);
    });
}
</script>
