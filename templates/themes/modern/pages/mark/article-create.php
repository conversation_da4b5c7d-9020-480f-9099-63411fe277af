<?php
// Article create form for mark layout
$pageTitle = $title ?? 'Create Article';
$currentRoute = 'mark-article-create';
$formData = $formData ?? [];
$error = $error ?? null;
?>

<!-- Article Create Form -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Nový článok</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Vytvorte nový článok alebo produkt</p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/articles" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Späť na zoznam
            </a>
        </div>
    </div>

    <?php if ($error): ?>
    <!-- Error Message -->
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Chyba pri načítavaní formulára</h3>
                <p class="mt-1 text-sm text-red-700 dark:text-red-300"><?php echo htmlspecialchars($error); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Article Form -->
    <form id="articleForm" class="space-y-6">

        <!-- Article Type Selection -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Typ článku</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <?php if (!empty($formData['article_types'])): ?>
                    <?php foreach ($formData['article_types'] as $type): ?>
                    <label class="article-type-option cursor-pointer">
                        <input type="radio" name="article_type" value="<?php echo htmlspecialchars($type['value']); ?>"
                               class="sr-only article-type-radio"
                               data-requires-price="<?php echo $type['requires_price'] ? 'true' : 'false'; ?>"
                               data-requires-sku="<?php echo $type['requires_sku'] ? 'true' : 'false'; ?>"
                               data-supports-inventory="<?php echo $type['supports_inventory'] ? 'true' : 'false'; ?>"
                               data-supports-digital-delivery="<?php echo $type['supports_digital_delivery'] ? 'true' : 'false'; ?>">
                        <div class="border-2 border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-<?php echo $type['color']; ?>-300 transition-colors article-type-card">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-<?php echo $type['color']; ?>-100 dark:bg-<?php echo $type['color']; ?>-900/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-<?php echo $type['color']; ?>-600 dark:text-<?php echo $type['color']; ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $type['icon']; ?>"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100"><?php echo htmlspecialchars($type['label']); ?></h4>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1"><?php echo htmlspecialchars($type['description']); ?></p>
                                </div>
                            </div>
                        </div>
                    </label>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-span-full text-center text-gray-500 dark:text-gray-400">
                        <p>Typy článkov nie sú dostupné</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Základné informácie</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Title -->
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Názov článku *
                    </label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="Zadajte názov článku...">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Názov bude použitý aj pre automatické generovanie URL</p>
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        URL slug
                    </label>
                    <input type="text" id="slug" name="slug"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="automaticky-generovany-slug">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Nechajte prázdne pre automatické generovanie</p>
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Stav článku
                    </label>
                    <select id="status" name="status"
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                        <?php if (!empty($formData['article_statuses'])): ?>
                            <?php foreach ($formData['article_statuses'] as $status): ?>
                            <option value="<?php echo htmlspecialchars($status['value']); ?>"
                                    <?php echo $status['value'] === 'draft' ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($status['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Excerpt -->
                <div class="lg:col-span-2">
                    <label for="excerpt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Krátky popis (excerpt)
                    </label>
                    <textarea id="excerpt" name="excerpt" rows="3"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                              placeholder="Krátky popis článku pre náhľady a vyhľadávače..."></textarea>
                </div>

                <!-- Image -->
                <div class="lg:col-span-2">
                    <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Obrázok
                    </label>
                    <input type="url" id="image" name="image"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="https://example.com/image.jpg">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">URL adresa obrázka</p>
                </div>

            </div>
        </div>

        <!-- Product Information (hidden by default) -->
        <div id="productFields" class="glass rounded-xl p-6 hidden">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Informácie o produkte</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Price -->
                <div id="priceField">
                    <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Cena *
                    </label>
                    <div class="flex">
                        <input type="number" id="price" name="price" step="0.01" min="0"
                               class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               placeholder="0.00">
                        <select id="currency" name="currency"
                                class="px-4 py-2 border-l-0 border-gray-300 dark:border-gray-600 rounded-r-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                            <?php if (!empty($formData['currencies'])): ?>
                                <?php foreach ($formData['currencies'] as $currency): ?>
                                <option value="<?php echo htmlspecialchars($currency['value']); ?>"
                                        <?php echo $currency['value'] === 'EUR' ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($currency['label']); ?>
                                </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <!-- SKU -->
                <div id="skuField">
                    <label for="sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        SKU
                    </label>
                    <input type="text" id="sku" name="sku"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="PROD-EXAMPLE-001">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Nechajte prázdne pre automatické generovanie</p>
                </div>

                <!-- Inventory (for physical products) -->
                <div id="inventoryField" class="hidden">
                    <label for="inventory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Skladové zásoby
                    </label>
                    <input type="number" id="inventory" name="inventory" min="0"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="0">
                </div>

                <!-- Digital Delivery Type (for digital products) -->
                <div id="digitalDeliveryField" class="hidden">
                    <label for="digital_delivery_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Typ doručenia *
                    </label>
                    <select id="digital_delivery_type" name="digital_delivery_type"
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                        <option value="">Vyberte typ doručenia</option>
                        <?php if (!empty($formData['digital_delivery_types'])): ?>
                            <?php foreach ($formData['digital_delivery_types'] as $deliveryType): ?>
                            <option value="<?php echo htmlspecialchars($deliveryType['value']); ?>">
                                <?php echo htmlspecialchars($deliveryType['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Digital File -->
                <div id="digitalFileField" class="hidden">
                    <label for="digital_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Súbor/Odkaz
                    </label>
                    <input type="text" id="digital_file" name="digital_file"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="/files/product.pdf alebo https://example.com/access">
                </div>

            </div>
        </div>

        <!-- Content -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Obsah článku</h3>

            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Obsah *
                </label>
                <textarea id="content" name="content" rows="15" required
                          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                          placeholder="Napíšte obsah článku..."></textarea>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Podporuje HTML a Markdown</p>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">SEO nastavenia</h3>
            <div class="space-y-4">

                <!-- Meta Title -->
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Meta Title
                    </label>
                    <input type="text" id="meta_title" name="meta_title" maxlength="60"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="SEO optimalizovaný názov (max 60 znakov)">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Nechajte prázdne pre použitie názvu článku</p>
                </div>

                <!-- Meta Description -->
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Meta Description
                    </label>
                    <textarea id="meta_description" name="meta_description" rows="3" maxlength="160"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                              placeholder="Popis článku pre vyhľadávače (max 160 znakov)"></textarea>
                </div>

                <!-- Meta Keywords -->
                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Kľúčové slová
                    </label>
                    <input type="text" id="meta_keywords" name="meta_keywords"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           placeholder="gitara, hudba, nástroj">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Oddeľte čiarkami</p>
                </div>

            </div>
        </div>

        <!-- Publishing Options -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Možnosti publikovania</h3>
            <div class="space-y-4">

                <!-- Featured -->
                <div class="flex items-center">
                    <input type="checkbox" id="is_featured" name="is_featured" value="1"
                           class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500">
                    <label for="is_featured" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Označiť ako odporúčaný článok
                    </label>
                </div>

                <!-- Public -->
                <div class="flex items-center">
                    <input type="checkbox" id="is_public" name="is_public" value="1" checked
                           class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500">
                    <label for="is_public" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Verejne dostupný
                    </label>
                </div>

            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                * Povinné polia
            </div>
            <div class="flex space-x-3">
                <button type="button" onclick="saveDraft()" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Uložiť koncept
                </button>
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Vytvoriť článok
                </button>
            </div>
        </div>

    </form>

</div>

<script>
// Article create form JavaScript
console.log('Article Create - Initializing...');

// Form elements
const form = document.getElementById('articleForm');
const titleInput = document.getElementById('title');
const slugInput = document.getElementById('slug');
const typeRadios = document.querySelectorAll('.article-type-radio');
const productFields = document.getElementById('productFields');

// Type-specific fields
const priceField = document.getElementById('priceField');
const skuField = document.getElementById('skuField');
const inventoryField = document.getElementById('inventoryField');
const digitalDeliveryField = document.getElementById('digitalDeliveryField');
const digitalFileField = document.getElementById('digitalFileField');

// Auto-generate slug from title
titleInput.addEventListener('input', function() {
    if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') {
        const slug = generateSlug(this.value);
        slugInput.value = slug;
        slugInput.dataset.autoGenerated = 'true';
    }
});

// Manual slug editing
slugInput.addEventListener('input', function() {
    this.dataset.autoGenerated = 'false';
});

// Article type selection
typeRadios.forEach(radio => {
    radio.addEventListener('change', function() {
        updateTypeSelection();
        updateProductFields();
    });
});

function updateTypeSelection() {
    // Update visual selection
    document.querySelectorAll('.article-type-card').forEach(card => {
        card.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
        card.classList.add('border-gray-200', 'dark:border-gray-700');
    });

    const selectedRadio = document.querySelector('.article-type-radio:checked');
    if (selectedRadio) {
        const card = selectedRadio.closest('.article-type-option').querySelector('.article-type-card');
        card.classList.remove('border-gray-200', 'dark:border-gray-700');
        card.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
    }
}

function updateProductFields() {
    const selectedRadio = document.querySelector('.article-type-radio:checked');

    if (!selectedRadio) {
        productFields.classList.add('hidden');
        return;
    }

    const requiresPrice = selectedRadio.dataset.requiresPrice === 'true';
    const requiresSku = selectedRadio.dataset.requiresSku === 'true';
    const supportsInventory = selectedRadio.dataset.supportsInventory === 'true';
    const supportsDigitalDelivery = selectedRadio.dataset.supportsDigitalDelivery === 'true';

    // Show/hide product fields section
    if (requiresPrice || requiresSku || supportsInventory || supportsDigitalDelivery) {
        productFields.classList.remove('hidden');
    } else {
        productFields.classList.add('hidden');
        return;
    }

    // Show/hide specific fields
    priceField.style.display = requiresPrice ? 'block' : 'none';
    skuField.style.display = requiresSku ? 'block' : 'none';
    inventoryField.style.display = supportsInventory ? 'block' : 'none';
    digitalDeliveryField.style.display = supportsDigitalDelivery ? 'block' : 'none';
    digitalFileField.style.display = supportsDigitalDelivery ? 'block' : 'none';

    // Update required attributes
    document.getElementById('price').required = requiresPrice;
    document.getElementById('digital_delivery_type').required = supportsDigitalDelivery;
}

function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/[\s-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

// Form submission
form.addEventListener('submit', async function(e) {
    e.preventDefault();

    if (!validateForm()) {
        return;
    }

    await submitForm('create');
});

function saveDraft() {
    // Set status to draft and submit
    document.getElementById('status').value = 'draft';
    submitForm('draft');
}

async function submitForm(action) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Add action type
    data.action = action;

    try {
        const response = await fetch('/mark/articles/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
            alert(result.message);
            if (result.redirect) {
                window.location.href = result.redirect;
            } else {
                window.location.href = '/mark/articles';
            }
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Submit error:', error);
        alert('Nastala chyba pri ukladaní článku');
    }
}

function validateForm() {
    // Check if article type is selected
    const selectedType = document.querySelector('.article-type-radio:checked');
    if (!selectedType) {
        alert('Vyberte typ článku');
        return false;
    }

    // Check required fields based on type
    const requiresPrice = selectedType.dataset.requiresPrice === 'true';
    const supportsDigitalDelivery = selectedType.dataset.supportsDigitalDelivery === 'true';

    if (requiresPrice) {
        const price = document.getElementById('price').value;
        if (!price || parseFloat(price) <= 0) {
            alert('Zadajte platnú cenu pre produkt');
            return false;
        }
    }

    if (supportsDigitalDelivery) {
        const deliveryType = document.getElementById('digital_delivery_type').value;
        if (!deliveryType) {
            alert('Vyberte typ doručenia pre digitálny produkt');
            return false;
        }
    }

    return true;
}

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    // Select first article type by default
    const firstType = document.querySelector('.article-type-radio');
    if (firstType) {
        firstType.checked = true;
        updateTypeSelection();
        updateProductFields();
    }
});
</script>
