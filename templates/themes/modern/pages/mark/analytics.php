<?php
// Analytics page for mark layout
$pageTitle = $title ?? 'Analytics';
$currentRoute = 'mark-analytics';
?>

<!-- Analytics Dashboard -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Analytics</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Preh<PERSON><PERSON> výkonnosti a štatistík</p>
        </div>
        <div class="flex space-x-3">
            <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                <option>Posledných 7 dní</option>
                <option>Posledných 30 dní</option>
                <option>Posledných 90 dní</option>
                <option>Tento rok</option>
            </select>
            <button onclick="refreshAnalytics()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Views -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Celkové zobrazenia</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalViews">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="viewsGrowth">
                        <span class="loading-skeleton">+-- %</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Unique Visitors -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unikátni návštevníci</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="uniqueVisitors">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="visitorsGrowth">
                        <span class="loading-skeleton">+-- %</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Bounce Rate -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Bounce Rate</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="bounceRate">
                        <span class="loading-skeleton">--%</span>
                    </p>
                    <p class="text-xs text-red-600 dark:text-red-400" id="bounceRateChange">
                        <span class="loading-skeleton">+-- %</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Average Session -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Priemerná relácia</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="avgSession">
                        <span class="loading-skeleton">--:--</span>
                    </p>
                    <p class="text-xs text-blue-600 dark:text-blue-400" id="sessionChange">
                        <span class="loading-skeleton">+-- %</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Traffic Chart -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Návštevnosť</h3>
                <div class="flex space-x-2">
                    <button class="text-sm px-3 py-1 bg-primary-100 text-primary-700 rounded-lg">Zobrazenia</button>
                    <button class="text-sm px-3 py-1 text-gray-500 hover:text-gray-700 rounded-lg">Návštevníci</button>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <div class="text-center">
                    <svg class="w-12 h-12 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400">Graf návštevnosti</p>
                    <p class="text-sm text-gray-400">Integrácia s Google Analytics</p>
                </div>
            </div>
        </div>

        <!-- Top Pages -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Najnavštevovanejšie stránky</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">/</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Domovská stránka</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">1,234</p>
                        <p class="text-xs text-green-600">+12%</p>
                    </div>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">/articles</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Články</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">856</p>
                        <p class="text-xs text-green-600">+8%</p>
                    </div>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">/products</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Produkty</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">642</p>
                        <p class="text-xs text-red-600">-3%</p>
                    </div>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">/about</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">O nás</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">423</p>
                        <p class="text-xs text-green-600">+5%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Traffic Sources -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Zdroje návštevnosti</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Organické vyhľadávanie</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">45%</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Priama návšteva</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">32%</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Sociálne siete</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">15%</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Referral</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">8%</span>
                </div>
            </div>
        </div>

        <!-- Device Types -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Typy zariadení</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Desktop</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">58%</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Mobile</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">35%</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Tablet</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">7%</span>
                </div>
            </div>
        </div>

        <!-- Real-time -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Real-time</h3>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2" id="activeUsers">
                    <span class="loading-skeleton">--</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Aktívni používatelia</p>
                <div class="flex items-center justify-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">Live</span>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
// Analytics JavaScript
console.log('Analytics - Initializing...');

// Mock data for demonstration
const mockAnalytics = {
    totalViews: 12543,
    viewsGrowth: '+15.3%',
    uniqueVisitors: 8921,
    visitorsGrowth: '+12.7%',
    bounceRate: '42%',
    bounceRateChange: '-2.1%',
    avgSession: '3:24',
    sessionChange: '****%',
    activeUsers: Math.floor(Math.random() * 50) + 10
};

function loadAnalyticsData() {
    console.log('Analytics - Loading data...');

    // Simulate loading delay
    setTimeout(() => {
        updateAnalyticsDisplay(mockAnalytics);
    }, 1000);
}

function updateAnalyticsDisplay(data) {
    console.log('Analytics - Updating display:', data);

    document.getElementById('totalViews').textContent = data.totalViews.toLocaleString();
    document.getElementById('viewsGrowth').textContent = data.viewsGrowth;
    document.getElementById('uniqueVisitors').textContent = data.uniqueVisitors.toLocaleString();
    document.getElementById('visitorsGrowth').textContent = data.visitorsGrowth;
    document.getElementById('bounceRate').textContent = data.bounceRate;
    document.getElementById('bounceRateChange').textContent = data.bounceRateChange;
    document.getElementById('avgSession').textContent = data.avgSession;
    document.getElementById('sessionChange').textContent = data.sessionChange;
    document.getElementById('activeUsers').textContent = data.activeUsers;
}

function refreshAnalytics() {
    console.log('Analytics - Refreshing...');

    // Show loading state
    document.querySelectorAll('.loading-skeleton').forEach(el => {
        el.textContent = '--';
    });

    // Reload data
    loadAnalyticsData();
}

// Auto-refresh active users every 30 seconds
setInterval(() => {
    const activeUsers = Math.floor(Math.random() * 50) + 10;
    document.getElementById('activeUsers').textContent = activeUsers;
}, 30000);

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAnalyticsData();
});
</script>
