<?php
// User edit content will be included in mark layout
$pageTitle = $title ?? 'Edit User';
$currentRoute = 'mark-user-edit';
?>

<!-- Edit User -->
<div class="space-y-6">

    <!-- Error Notification -->
    <?php if (isset($error)): ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.showNotification) {
                window.showNotification('<?php echo addslashes($error); ?>', 'error');
            }
        });
        </script>
    <?php endif; ?>

    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="/mark/users" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Upraviť používateľa</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Upravte údaje používateľa</p>
        </div>
    </div>

    <!-- Edit User Form -->
    <div class="glass rounded-xl overflow-hidden">
        <form method="POST" action="/mark/users/<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>/edit" class="space-y-6 p-6">

            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Osobné údaje</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Meno <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" required class="form-input"
                               value="<?php echo htmlspecialchars($user['first_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="Zadajte meno">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Priezvisko <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" required class="form-input"
                               value="<?php echo htmlspecialchars($user['last_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="Zadajte priezvisko">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="email" required class="form-input"
                               value="<?php echo htmlspecialchars($user['email'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Telefón
                        </label>
                        <input type="tel" name="phone" class="form-input"
                               value="<?php echo htmlspecialchars($user['phone'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="+421 900 123 456">
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Účet</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Používateľské meno <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="username" required class="form-input"
                               value="<?php echo htmlspecialchars($user['username'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="username">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Minimálne 3 znaky, len písmená, čísla a podčiarkovník</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Rola <span class="text-red-500">*</span>
                        </label>
                        <select name="role" required class="form-select">
                            <option value="">Vyberte rolu</option>
                            <option value="user" <?php echo ($user['role'] ?? '') === 'user' ? 'selected' : ''; ?>>Používateľ</option>
                            <option value="editor" <?php echo ($user['role'] ?? '') === 'editor' ? 'selected' : ''; ?>>Editor</option>
                            <option value="admin" <?php echo ($user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrátor</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Password Change -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Zmena hesla</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Nové heslo
                        </label>
                        <input type="password" name="password" class="form-input" placeholder="••••••••">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Nechajte prázdne ak nechcete zmeniť heslo</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Potvrdiť nové heslo
                        </label>
                        <input type="password" name="password_confirm" class="form-input" placeholder="••••••••">
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Oprávnenia</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Aktívny účet</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Používateľ sa môže prihlásiť do systému</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="is_active" value="1"
                                   <?php echo ($user['is_active'] ?? false) ? 'checked' : ''; ?>
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Email notifikácie</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Posielať email notifikácie používateľovi</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_notifications" value="1"
                                   <?php echo ($user['email_notifications'] ?? true) ? 'checked' : ''; ?>
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Vynútiť zmenu hesla</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Používateľ musí zmeniť heslo pri ďalšom prihlásení</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="force_password_change" value="1"
                                   <?php echo ($user['force_password_change'] ?? false) ? 'checked' : ''; ?>
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Dodatočné informácie</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Oddelenie
                        </label>
                        <select name="department" class="form-select">
                            <option value="">Vyberte oddelenie</option>
                            <option value="it" <?php echo ($user['department'] ?? '') === 'it' ? 'selected' : ''; ?>>IT</option>
                            <option value="marketing" <?php echo ($user['department'] ?? '') === 'marketing' ? 'selected' : ''; ?>>Marketing</option>
                            <option value="sales" <?php echo ($user['department'] ?? '') === 'sales' ? 'selected' : ''; ?>>Predaj</option>
                            <option value="support" <?php echo ($user['department'] ?? '') === 'support' ? 'selected' : ''; ?>>Podpora</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pozícia
                        </label>
                        <input type="text" name="position" class="form-input"
                               value="<?php echo htmlspecialchars($user['position'] ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                               placeholder="Pozícia v spoločnosti">
                    </div>
                </div>
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Poznámky
                    </label>
                    <textarea name="notes" rows="3" class="form-textarea"
                              placeholder="Dodatočné poznámky o používateľovi..."><?php echo htmlspecialchars($user['notes'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>
                </div>
            </div>

        </form>

        <!-- Form Actions -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-end space-x-3">
                <a href="/mark/users" class="btn btn-secondary">Zrušiť</a>
                <button type="submit" form="editUserForm" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Uložiť zmeny
                </button>
            </div>
        </div>
    </div>

</div>

<script>
// Add form ID to the form element
document.querySelector('form').id = 'editUserForm';

// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.querySelector('input[name="password"]');
    const confirmField = document.querySelector('input[name="password_confirm"]');

    function validatePasswords() {
        if (passwordField.value && confirmField.value) {
            if (passwordField.value !== confirmField.value) {
                confirmField.setCustomValidity('Heslá sa nezhodujú');
            } else {
                confirmField.setCustomValidity('');
            }
        } else {
            confirmField.setCustomValidity('');
        }
    }

    passwordField.addEventListener('input', validatePasswords);
    confirmField.addEventListener('input', validatePasswords);
});
</script>
