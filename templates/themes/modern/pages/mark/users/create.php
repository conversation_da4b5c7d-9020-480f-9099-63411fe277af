<?php
// User create content will be included in mark layout
$pageTitle = $title ?? 'Create User';
$currentRoute = 'mark-user-create';
?>

<!-- Create User -->
<div class="space-y-6">

    <!-- Error Notification -->
    <?php if (isset($error)): ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.showNotification) {
                window.showNotification('<?php echo addslashes($error); ?>', 'error');
            }
        });
        </script>
    <?php endif; ?>

    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="/mark/users" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Vytvoriť nového používateľa</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Pridajte nový používateľský účet do systému</p>
        </div>
    </div>

    <!-- Create User Form -->
    <div class="glass rounded-xl overflow-hidden">
        <form action="/mark/users/create" method="POST" class="space-y-6 p-6">

            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Osobné údaje</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Meno <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="first_name" required class="form-input" placeholder="Zadajte meno" value="<?php echo htmlspecialchars($formData['first_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Priezvisko <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="last_name" required class="form-input" placeholder="Zadajte priezvisko" value="<?php echo htmlspecialchars($formData['last_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="email" required class="form-input" placeholder="<EMAIL>" value="<?php echo htmlspecialchars($formData['email'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Telefón
                        </label>
                        <input type="tel" name="phone" class="form-input" placeholder="+421 900 123 456" value="<?php echo htmlspecialchars($formData['phone'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Účet</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Používateľské meno <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="username" required class="form-input" placeholder="username" value="<?php echo htmlspecialchars($formData['username'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Minimálne 3 znaky, len písmená, čísla a podčiarkovník</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Rola <span class="text-red-500">*</span>
                        </label>
                        <select name="role" required class="form-select">
                            <option value="">Vyberte rolu</option>
                            <option value="user" <?php echo ($formData['role'] ?? '') === 'user' ? 'selected' : ''; ?>>Používateľ</option>
                            <option value="editor" <?php echo ($formData['role'] ?? '') === 'editor' ? 'selected' : ''; ?>>Editor</option>
                            <option value="admin" <?php echo ($formData['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrátor</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Heslo <span class="text-red-500">*</span>
                        </label>
                        <input type="password" name="password" required class="form-input" placeholder="••••••••">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Minimálne 6 znakov</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Potvrdiť heslo <span class="text-red-500">*</span>
                        </label>
                        <input type="password" name="password_confirm" required class="form-input" placeholder="••••••••">
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Oprávnenia</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Aktívny účet</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Používateľ sa môže prihlásiť do systému</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="is_active" value="1" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Email notifikácie</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Posielať email notifikácie používateľovi</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Vynútiť zmenu hesla</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Používateľ musí zmeniť heslo pri prvom prihlásení</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Dodatočné informácie</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Oddelenie
                        </label>
                        <select class="form-select">
                            <option value="">Vyberte oddelenie</option>
                            <option value="it">IT</option>
                            <option value="marketing">Marketing</option>
                            <option value="sales">Predaj</option>
                            <option value="support">Podpora</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pozícia
                        </label>
                        <input type="text" class="form-input" placeholder="Pozícia v spoločnosti">
                    </div>
                </div>
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Poznámky
                    </label>
                    <textarea rows="3" class="form-textarea" placeholder="Dodatočné poznámky o používateľovi..."></textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <div class="flex justify-end space-x-3">
                    <a href="/mark/users" class="btn btn-secondary">Zrušiť</a>
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Vytvoriť používateľa
                    </button>
                </div>
            </div>

        </form>
    </div>

</div>
