<?php
// Dashboard content will be included in mark layout
$pageTitle = $title ?? 'Dashboard';
$currentRoute = 'mark-dashboard';
?>

<!-- Dashboard Overview -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Dashboard</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Prehľad systému a štatistiky</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="refreshDashboard()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
            <button onclick="exportReport()" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

        <!-- Users -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Používatelia</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalUsers">
                        <span class="loading-skeleton">---</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="usersGrowth">
                        <span class="loading-skeleton">+-- tento mesiac</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Články</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalArticles">
                        <span class="loading-skeleton">---</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="articlesGrowth">
                        <span class="loading-skeleton">+-- tento týždeň</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Page Views -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Zobrazenia</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalViews">
                        <span class="loading-skeleton">---</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="viewsGrowth">
                        <span class="loading-skeleton">+-- tento mesiac</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Systém</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400" id="systemStatus">
                        <span class="loading-skeleton">---</span>
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400" id="systemUptime">
                        <span class="loading-skeleton">--% uptime</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

        <!-- Recent Activity -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Posledná aktivita</h3>
                <a href="/mark/logs" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                    Zobraziť všetko
                </a>
            </div>
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900 dark:text-gray-100">Nový používateľ sa zaregistroval</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">pred 5 minútami</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900 dark:text-gray-100">Článok bol publikovaný</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">pred 1 hodinou</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900 dark:text-gray-100">Systémová aktualizácia</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">pred 3 hodinami</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900 dark:text-gray-100">Backup dokončený</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">pred 6 hodinami</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Rýchle akcie</h3>
            <div class="grid grid-cols-2 gap-4">
                <a href="/mark/users/create" class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-700 dark:text-blue-300">Nový používateľ</span>
                </a>

                <a href="/mark/articles/create" class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                    <svg class="w-8 h-8 text-green-600 dark:text-green-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span class="text-sm font-medium text-green-700 dark:text-green-300">Nový článok</span>
                </a>

                <a href="/mark/settings" class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                    <svg class="w-8 h-8 text-purple-600 dark:text-purple-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="text-sm font-medium text-purple-700 dark:text-purple-300">Nastavenia</span>
                </a>

                <a href="/mark/analytics" class="flex flex-col items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
                    <svg class="w-8 h-8 text-orange-600 dark:text-orange-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="text-sm font-medium text-orange-700 dark:text-orange-300">Analytika</span>
                </a>
            </div>
        </div>

    </div>

</div>

<!-- Dashboard JavaScript -->
<script>
// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard - Initializing...');

    // Load dashboard data
    loadDashboardData();

    // Set up auto-refresh every 30 seconds
    setInterval(loadDashboardData, 30000);

    console.log('Dashboard - Initialized');
});

// Load dashboard data from API
async function loadDashboardData() {
    console.log('Dashboard - Loading data...');

    try {
        // Simulate API call with real data from UserListService
        const response = await fetch('/mark/api/dashboard/stats');

        if (response.ok) {
            const data = await response.json();
            updateDashboardStats(data);
        } else {
            // Fallback to mock data if API not available
            console.log('Dashboard - API not available, using mock data');
            setTimeout(() => {
                updateDashboardStats(getMockData());
            }, 1000);
        }
    } catch (error) {
        console.log('Dashboard - Error loading data, using mock data:', error);
        setTimeout(() => {
            updateDashboardStats(getMockData());
        }, 1000);
    }
}

// Update dashboard statistics
function updateDashboardStats(data) {
    console.log('Dashboard - Updating stats:', data);

    // Update user stats
    document.getElementById('totalUsers').textContent = data.totalUsers;
    document.getElementById('usersGrowth').textContent = data.usersGrowth;

    // Update article stats
    document.getElementById('totalArticles').textContent = data.totalArticles;
    document.getElementById('articlesGrowth').textContent = data.articlesGrowth;

    // Update view stats
    document.getElementById('totalViews').textContent = data.totalViews;
    document.getElementById('viewsGrowth').textContent = data.viewsGrowth;

    // Update system stats
    document.getElementById('systemStatus').textContent = data.systemStatus;
    document.getElementById('systemUptime').textContent = data.systemUptime;

    // Update recent activity
    updateRecentActivity(data.recentActivity || []);

    console.log('Dashboard - Stats updated successfully');
}

// Update recent activity section
function updateRecentActivity(activities) {
    const activityContainer = document.querySelector('#recentActivity, .space-y-4');
    if (!activityContainer) return;

    if (activities.length === 0) {
        activities = [
            {
                type: 'user',
                message: 'Nový používateľ sa zaregistroval',
                time: 'pred 5 minútami',
                color: 'bg-green-500'
            },
            {
                type: 'article',
                message: 'Článok bol publikovaný',
                time: 'pred 1 hodinou',
                color: 'bg-blue-500'
            },
            {
                type: 'system',
                message: 'Systémová aktualizácia',
                time: 'pred 3 hodinami',
                color: 'bg-yellow-500'
            }
        ];
    }

    activityContainer.innerHTML = activities.map(activity => `
        <div class="flex items-start space-x-3">
            <div class="w-2 h-2 ${activity.color} rounded-full mt-2"></div>
            <div class="flex-1">
                <p class="text-sm text-gray-900 dark:text-gray-100">${activity.message}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">${activity.time}</p>
            </div>
        </div>
    `).join('');
}

// Get mock data for development
function getMockData() {
    return {
        totalUsers: 142,
        usersGrowth: '+12% tento mesiac',
        totalArticles: 67,
        articlesGrowth: '+5 tento týždeň',
        totalViews: '45.2K',
        viewsGrowth: '+8% tento mesiac',
        systemStatus: 'Online',
        systemUptime: '99.9% uptime',
        recentActivity: [
            {
                type: 'user',
                message: 'Nový používateľ sa zaregistroval',
                time: 'pred 5 minútami',
                color: 'bg-green-500'
            },
            {
                type: 'article',
                message: 'Článok "Nové funkcie" bol publikovaný',
                time: 'pred 15 minútami',
                color: 'bg-blue-500'
            },
            {
                type: 'system',
                message: 'Systémové nastavenia boli aktualizované',
                time: 'pred 1 hodinou',
                color: 'bg-purple-500'
            },
            {
                type: 'backup',
                message: 'Backup dokončený úspešne',
                time: 'pred 6 hodinami',
                color: 'bg-yellow-500'
            }
        ]
    };
}

// Refresh dashboard manually
function refreshDashboard() {
    if (window.showNotification) {
        window.showNotification('Dashboard sa obnovuje...', 'info');
    }
    loadDashboardData();
}

// Export dashboard report
function exportReport() {
    if (window.showNotification) {
        window.showNotification('Export reportu nie je implementovaný', 'warning');
    }
}
</script>
