<?php
// Error Logs content will be included in mark layout
$pageTitle = $title ?? 'Error Logs';
$currentRoute = 'mark-logs-errors';
?>

<!-- Error Logs -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Error Logs</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Chyby a kritické problémy systému</p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/logs" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Activity Logs
            </a>
            <a href="/mark/logs/system" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                </svg>
                System Logs
            </a>
            <button onclick="refreshErrorLogs()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
            <button onclick="clearErrorLogs()" class="btn btn-danger">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Vymazať
            </button>
        </div>
    </div>

    <!-- Error Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">

        <!-- Critical Errors -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/20">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Kritické chyby</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400" id="criticalErrors">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Today's Errors -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
                    <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Dnes</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="todayErrors">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- This Week -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tento týždeň</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="weekErrors">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Error Rate -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/20">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Error Rate</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="errorRate">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

    </div>

    <!-- Filters -->
    <div class="glass rounded-xl p-6">
        <div class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Severity</label>
                <select id="severityFilter" class="form-select w-32" onchange="filterErrorLogs()">
                    <option value="">Všetky</option>
                    <option value="CRITICAL">Critical</option>
                    <option value="ERROR">Error</option>
                    <option value="WARNING">Warning</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Kategória</label>
                <select id="categoryFilter" class="form-select w-40" onchange="filterErrorLogs()">
                    <option value="">Všetky kategórie</option>
                    <option value="database">Database</option>
                    <option value="authentication">Authentication</option>
                    <option value="file_system">File System</option>
                    <option value="network">Network</option>
                    <option value="validation">Validation</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum od</label>
                <input type="date" id="dateFromFilter" class="form-input w-40" onchange="filterErrorLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum do</label>
                <input type="date" id="dateToFilter" class="form-input w-40" onchange="filterErrorLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hľadať</label>
                <input type="text" id="searchFilter" class="form-input w-48" placeholder="Hľadať v chybách..." onkeyup="filterErrorLogs()">
            </div>
            <div class="flex flex-col">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">&nbsp;</label>
                <button onclick="clearFilters()" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Vymazať filtre
                </button>
            </div>
        </div>
    </div>

    <!-- Error Entries -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Error Logs</h3>
        </div>

        <div class="space-y-0" id="errorEntries">
            <!-- Loading state -->
            <div class="p-6 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                <p class="text-sm text-gray-500 dark:text-gray-400">Načítavam error logy...</p>
            </div>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje sa <span id="showingFrom">1</span> až <span id="showingTo">50</span> z <span id="totalCount">0</span> záznamov
                </div>
                <div class="flex space-x-2">
                    <button onclick="previousPage()" id="prevButton" class="btn btn-secondary" disabled>
                        Predchádzajúca
                    </button>
                    <button onclick="nextPage()" id="nextButton" class="btn btn-secondary">
                        Ďalšia
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
// Error logs functionality
let currentPage = 1;
let currentLimit = 50;
let allErrorLogs = [];
let filteredErrorLogs = [];

document.addEventListener('DOMContentLoaded', function() {
    console.log('Error Logs - Initializing...');

    // Load error logs
    loadErrorLogs();

    // Set up auto-refresh every 60 seconds
    setInterval(loadErrorLogs, 60000);

    console.log('Error Logs - Initialized');
});

// Load error logs from API
async function loadErrorLogs() {
    console.log('Error Logs - Loading data...');

    try {
        const response = await fetch('/mark/api/system/errors?limit=1000');

        if (response.ok) {
            const data = await response.json();
            allErrorLogs = data.logs || [];
            updateErrorStatistics(data);
            filterErrorLogs();
        } else {
            console.error('Failed to load error logs:', response.statusText);
            showErrorMessage('Nepodarilo sa načítať error logy');
        }
    } catch (error) {
        console.error('Error loading error logs:', error);
        showErrorMessage('Chyba pri načítavaní error logov');
    }
}

// Update error statistics
function updateErrorStatistics(data) {
    document.getElementById('criticalErrors').textContent = data.critical || 0;
    document.getElementById('todayErrors').textContent = data.today || 0;
    document.getElementById('weekErrors').textContent = data.this_week || 0;
    document.getElementById('errorRate').textContent = '0.1%'; // Placeholder
}

// Show error message
function showErrorMessage(message) {
    const container = document.getElementById('errorEntries');
    container.innerHTML = `
        <div class="p-6 text-center text-red-600 dark:text-red-400">
            ${message}
        </div>
    `;
}

// Filter error logs
function filterErrorLogs() {
    const severityFilter = document.getElementById('severityFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

    filteredErrorLogs = allErrorLogs.filter(log => {
        // Severity filter
        if (severityFilter && log.severity !== severityFilter) {
            return false;
        }

        // Category filter
        if (categoryFilter && log.category !== categoryFilter) {
            return false;
        }

        // Date filters
        const logDate = log.timestamp.split(' ')[0];
        if (dateFrom && logDate < dateFrom) {
            return false;
        }
        if (dateTo && logDate > dateTo) {
            return false;
        }

        // Search filter
        if (searchFilter && !log.message.toLowerCase().includes(searchFilter) && !log.context.toLowerCase().includes(searchFilter)) {
            return false;
        }

        return true;
    });

    currentPage = 1;
    displayErrorLogs();
}

// Display error logs
function displayErrorLogs() {
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = startIndex + currentLimit;
    const pageData = filteredErrorLogs.slice(startIndex, endIndex);

    const container = document.getElementById('errorEntries');

    if (pageData.length === 0) {
        container.innerHTML = `
            <div class="p-6 text-center text-gray-500 dark:text-gray-400">
                Žiadne error logy nenájdené
            </div>
        `;
    } else {
        container.innerHTML = pageData.map(log => {
            const severityColor = getSeverityColor(log.severity);
            return `
                <div class="p-6 border-l-4 ${severityColor.border} ${severityColor.bg} hover:${severityColor.hover} transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded ${severityColor.badge}">
                                    ${log.severity}
                                </span>
                                <span class="inline-flex px-2 py-1 text-xs rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    ${log.category}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">${log.timestamp}</span>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                ${log.message}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                <strong>Context:</strong> ${log.context}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mb-2">
                                <strong>File:</strong> ${log.file}:${log.line}
                            </p>
                            <details class="mt-3">
                                <summary class="text-sm text-gray-600 dark:text-gray-400 cursor-pointer hover:text-gray-900 dark:hover:text-gray-100">
                                    Stack Trace
                                </summary>
                                <pre class="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-700 dark:text-gray-300 overflow-x-auto">${log.stack_trace}</pre>
                            </details>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    updatePagination();
}

// Get color scheme for severity
function getSeverityColor(severity) {
    const colors = {
        'CRITICAL': {
            border: 'border-red-600',
            bg: 'bg-red-50 dark:bg-red-900/10',
            hover: 'hover:bg-red-100 dark:hover:bg-red-900/20',
            badge: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300'
        },
        'ERROR': {
            border: 'border-red-500',
            bg: 'bg-red-50 dark:bg-red-900/10',
            hover: 'hover:bg-red-100 dark:hover:bg-red-900/20',
            badge: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300'
        },
        'WARNING': {
            border: 'border-yellow-500',
            bg: 'bg-yellow-50 dark:bg-yellow-900/10',
            hover: 'hover:bg-yellow-100 dark:hover:bg-yellow-900/20',
            badge: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300'
        }
    };

    return colors[severity] || colors['ERROR'];
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredErrorLogs.length / currentLimit);
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = Math.min(startIndex + currentLimit, filteredErrorLogs.length);

    document.getElementById('showingFrom').textContent = filteredErrorLogs.length > 0 ? startIndex + 1 : 0;
    document.getElementById('showingTo').textContent = endIndex;
    document.getElementById('totalCount').textContent = filteredErrorLogs.length;

    document.getElementById('prevButton').disabled = currentPage <= 1;
    document.getElementById('nextButton').disabled = currentPage >= totalPages;
}

// Pagination functions
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displayErrorLogs();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredErrorLogs.length / currentLimit);
    if (currentPage < totalPages) {
        currentPage++;
        displayErrorLogs();
    }
}

// Clear filters
function clearFilters() {
    document.getElementById('severityFilter').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    document.getElementById('searchFilter').value = '';
    filterErrorLogs();
}

// Refresh error logs
function refreshErrorLogs() {
    if (window.showNotification) {
        window.showNotification('Error logy sa obnovujú...', 'info');
    }
    loadErrorLogs();
}

// Clear error logs
function clearErrorLogs() {
    if (confirm('Naozaj chcete vymazať všetky error logy? Táto akcia sa nedá vrátiť späť.')) {
        if (window.showNotification) {
            window.showNotification('Vymazanie error logov nie je implementované', 'warning');
        }
    }
}
</script>
