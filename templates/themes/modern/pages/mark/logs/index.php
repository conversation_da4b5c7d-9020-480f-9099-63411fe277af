<?php
// Logs content will be included in mark layout
$pageTitle = $title ?? 'Activity Logs';
$currentRoute = 'mark-logs';
?>

<!-- Activity Logs -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Activity Logs</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Sledovanie aktivít používateľov a systému</p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/logs/system" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                </svg>
                System Logs
            </a>
            <button onclick="refreshLogs()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
            <button onclick="exportLogs()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export
            </button>
            <button onclick="clearLogs()" class="btn btn-danger">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Vymazať
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">

        <!-- Total Logs -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Celkom záznamov</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalLogs">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Today's Logs -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Dnes</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="todayLogs">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- This Week -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/20">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tento týždeň</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="weekLogs">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Error Count -->
        <div class="glass rounded-xl p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/20">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Chyby</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="errorLogs">
                        <span class="loading-skeleton">---</span>
                    </p>
                </div>
            </div>
        </div>

    </div>

    <!-- Filters -->
    <div class="glass rounded-xl p-6">
        <div class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Typ aktivity</label>
                <select id="typeFilter" class="form-select w-48" onchange="filterLogs()">
                    <option value="">Všetky typy</option>
                    <option value="user_created">Vytvorenie používateľa</option>
                    <option value="user_updated">Aktualizácia používateľa</option>
                    <option value="user_deleted">Zmazanie používateľa</option>
                    <option value="login_success">Úspešné prihlásenie</option>
                    <option value="login_failed">Neúspešné prihlásenie</option>
                    <option value="system_">Systémové udalosti</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum od</label>
                <input type="date" id="dateFrom" class="form-input w-40" onchange="filterLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum do</label>
                <input type="date" id="dateTo" class="form-input w-40" onchange="filterLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hľadať</label>
                <input type="text" id="searchFilter" class="form-input w-48" placeholder="Hľadať v správach..." onkeyup="filterLogs()">
            </div>
            <div class="flex flex-col">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">&nbsp;</label>
                <button onclick="clearFilters()" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Vymazať filtre
                </button>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Activity Logs</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Čas
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Typ
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Správa
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Používateľ
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            IP adresa
                        </th>
                    </tr>
                </thead>
                <tbody id="logsTableBody" class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Loading skeleton -->
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center">
                            <div class="flex items-center justify-center">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">Načítavam logy...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje sa <span id="showingFrom">1</span> až <span id="showingTo">50</span> z <span id="totalCount">0</span> záznamov
                </div>
                <div class="flex space-x-2">
                    <button onclick="previousPage()" id="prevButton" class="btn btn-secondary" disabled>
                        Predchádzajúca
                    </button>
                    <button onclick="nextPage()" id="nextButton" class="btn btn-secondary">
                        Ďalšia
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
// Logs functionality
let currentPage = 1;
let currentLimit = 50;
let allLogs = [];
let filteredLogs = [];

document.addEventListener('DOMContentLoaded', function() {
    console.log('Logs - Initializing...');

    // Load logs data
    loadLogs();

    // Set up auto-refresh every 30 seconds
    setInterval(loadLogs, 30000);

    console.log('Logs - Initialized');
});

// Load logs from API
async function loadLogs() {
    console.log('Logs - Loading data...');

    try {
        const response = await fetch('/mark/api/activity/logs?limit=1000'); // Get more for filtering

        if (response.ok) {
            const data = await response.json();
            allLogs = data.logs || [];
            updateStatistics(data);
            filterLogs(); // Apply current filters
        } else {
            console.error('Failed to load logs:', response.statusText);
            showError('Nepodarilo sa načítať logy');
        }
    } catch (error) {
        console.error('Error loading logs:', error);
        showError('Chyba pri načítavaní logov');
    }
}

// Update statistics
function updateStatistics(data) {
    document.getElementById('totalLogs').textContent = data.total || 0;
    document.getElementById('todayLogs').textContent = data.today || 0;
    document.getElementById('weekLogs').textContent = data.this_week || 0;

    // Count errors
    const errorCount = Object.entries(data.by_type || {})
        .filter(([type]) => type.includes('failed') || type.includes('error'))
        .reduce((sum, [, count]) => sum + count, 0);

    document.getElementById('errorLogs').textContent = errorCount;
}

// Filter logs based on current filters
function filterLogs() {
    const typeFilter = document.getElementById('typeFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

    filteredLogs = allLogs.filter(log => {
        // Type filter
        if (typeFilter && !log.type.includes(typeFilter)) {
            return false;
        }

        // Date filters
        const logDate = log.timestamp.split(' ')[0]; // Get date part
        if (dateFrom && logDate < dateFrom) {
            return false;
        }
        if (dateTo && logDate > dateTo) {
            return false;
        }

        // Search filter
        if (searchFilter && !log.message.toLowerCase().includes(searchFilter)) {
            return false;
        }

        return true;
    });

    currentPage = 1;
    displayLogs();
}

// Display logs in table
function displayLogs() {
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = startIndex + currentLimit;
    const pageData = filteredLogs.slice(startIndex, endIndex);

    const tbody = document.getElementById('logsTableBody');

    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    Žiadne logy nenájdené
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = pageData.map(log => {
            const typeColor = getTypeColor(log.type);
            return `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        ${log.timestamp}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeColor}">
                            ${formatType(log.type)}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        ${log.message}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        ${log.user || 'system'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        ${log.ip || '-'}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // Update pagination
    updatePagination();
}

// Get color for log type
function getTypeColor(type) {
    if (type.includes('created')) return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    if (type.includes('updated')) return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    if (type.includes('deleted')) return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    if (type.includes('success')) return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    if (type.includes('failed')) return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    if (type.includes('system')) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
}

// Format type for display
function formatType(type) {
    const typeMap = {
        'user_created': 'Vytvorenie',
        'user_updated': 'Aktualizácia',
        'user_deleted': 'Zmazanie',
        'login_success': 'Prihlásenie',
        'login_failed': 'Chyba prihlásenia'
    };

    return typeMap[type] || type;
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(filteredLogs.length / currentLimit);
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = Math.min(startIndex + currentLimit, filteredLogs.length);

    document.getElementById('showingFrom').textContent = filteredLogs.length > 0 ? startIndex + 1 : 0;
    document.getElementById('showingTo').textContent = endIndex;
    document.getElementById('totalCount').textContent = filteredLogs.length;

    document.getElementById('prevButton').disabled = currentPage <= 1;
    document.getElementById('nextButton').disabled = currentPage >= totalPages;
}

// Pagination functions
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displayLogs();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredLogs.length / currentLimit);
    if (currentPage < totalPages) {
        currentPage++;
        displayLogs();
    }
}

// Clear filters
function clearFilters() {
    document.getElementById('typeFilter').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    document.getElementById('searchFilter').value = '';
    filterLogs();
}

// Refresh logs
function refreshLogs() {
    if (window.showNotification) {
        window.showNotification('Logy sa obnovujú...', 'info');
    }
    loadLogs();
}

// Export logs
function exportLogs() {
    if (window.showNotification) {
        window.showNotification('Export logov nie je implementovaný', 'warning');
    }
}

// Clear logs
function clearLogs() {
    if (confirm('Naozaj chcete vymazať všetky logy? Táto akcia sa nedá vrátiť späť.')) {
        if (window.showNotification) {
            window.showNotification('Vymazanie logov nie je implementované', 'warning');
        }
    }
}

// Show error message
function showError(message) {
    const tbody = document.getElementById('logsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="5" class="px-6 py-4 text-center text-red-600 dark:text-red-400">
                ${message}
            </td>
        </tr>
    `;
}
</script>
