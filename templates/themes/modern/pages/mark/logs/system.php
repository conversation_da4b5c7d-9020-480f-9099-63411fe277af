<?php
// System Logs content will be included in mark layout
$pageTitle = $title ?? 'System Logs';
$currentRoute = 'mark-logs-system';
?>

<!-- System Logs -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">System Logs</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Systémové logy, chyby a debug informácie</p>
        </div>
        <div class="flex space-x-3">
            <a href="/mark/logs" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Activity Logs
            </a>
            <button onclick="refreshSystemLogs()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
            <button onclick="clearSystemLogs()" class="btn btn-danger">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Vymazať
            </button>
        </div>
    </div>

    <!-- Log Type Tabs -->
    <div class="glass rounded-xl p-6">
        <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
            <button onclick="switchLogType('system')" id="tab-system" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow">
                System Logs
            </button>
            <button onclick="switchLogType('error')" id="tab-error" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                Error Logs
            </button>
            <button onclick="switchLogType('debug')" id="tab-debug" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                Debug Logs
            </button>
            <button onclick="switchLogType('access')" id="tab-access" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                Access Logs
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="glass rounded-xl p-6">
        <div class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Log Level</label>
                <select id="levelFilter" class="form-select w-32" onchange="filterSystemLogs()">
                    <option value="">Všetky</option>
                    <option value="ERROR">Error</option>
                    <option value="WARNING">Warning</option>
                    <option value="INFO">Info</option>
                    <option value="DEBUG">Debug</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum od</label>
                <input type="date" id="dateFromFilter" class="form-input w-40" onchange="filterSystemLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dátum do</label>
                <input type="date" id="dateToFilter" class="form-input w-40" onchange="filterSystemLogs()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hľadať</label>
                <input type="text" id="searchFilter" class="form-input w-48" placeholder="Hľadať v logoch..." onkeyup="filterSystemLogs()">
            </div>
            <div class="flex flex-col">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">&nbsp;</label>
                <button onclick="clearFilters()" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Vymazať filtre
                </button>
            </div>
        </div>
    </div>

    <!-- Log Entries -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100" id="logTypeTitle">System Logs</h3>
        </div>

        <div class="space-y-0" id="logEntries">
            <!-- Loading state -->
            <div class="p-6 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                <p class="text-sm text-gray-500 dark:text-gray-400">Načítavam logy...</p>
            </div>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje sa <span id="showingFrom">1</span> až <span id="showingTo">50</span> z <span id="totalCount">0</span> záznamov
                </div>
                <div class="flex space-x-2">
                    <button onclick="previousPage()" id="prevButton" class="btn btn-secondary" disabled>
                        Predchádzajúca
                    </button>
                    <button onclick="nextPage()" id="nextButton" class="btn btn-secondary">
                        Ďalšia
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
// System logs functionality
let currentLogType = 'system';
let currentPage = 1;
let currentLimit = 50;
let allSystemLogs = [];
let filteredSystemLogs = [];

document.addEventListener('DOMContentLoaded', function() {
    console.log('System Logs - Initializing...');

    // Load system logs
    loadSystemLogs();

    // Set up auto-refresh every 60 seconds
    setInterval(loadSystemLogs, 60000);

    console.log('System Logs - Initialized');
});

// Load system logs from API
async function loadSystemLogs() {
    console.log('System Logs - Loading data...');

    try {
        const response = await fetch('/mark/api/system/logs?limit=1000');

        if (response.ok) {
            const data = await response.json();
            allSystemLogs = data.logs || [];
            updateSystemStatistics(data);
            filterSystemLogs();
        } else {
            console.error('Failed to load system logs:', response.statusText);
            showSystemError('Nepodarilo sa načítať system logy');
        }
    } catch (error) {
        console.error('Error loading system logs:', error);
        showSystemError('Chyba pri načítavaní system logov');
    }
}

// Update system statistics
function updateSystemStatistics(data) {
    // Update statistics if elements exist (they don't in system logs template)
    // This is for future enhancement
    console.log('System logs statistics:', data);
}

// Switch log type
function switchLogType(type) {
    currentLogType = type;

    // Update tab appearance
    document.querySelectorAll('[id^="tab-"]').forEach(tab => {
        tab.className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100';
    });

    document.getElementById(`tab-${type}`).className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow';

    // Update title
    const titles = {
        'system': 'System Logs',
        'error': 'Error Logs',
        'debug': 'Debug Logs',
        'access': 'Access Logs'
    };

    document.getElementById('logTypeTitle').textContent = titles[type];

    // Filter logs by type
    filterSystemLogs();
}

// Filter system logs
function filterSystemLogs() {
    const levelFilter = document.getElementById('levelFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

    filteredSystemLogs = allSystemLogs.filter(log => {
        // Level filter
        if (levelFilter && log.level !== levelFilter) {
            return false;
        }

        // Date filters
        const logDate = log.timestamp.split(' ')[0];
        if (dateFrom && logDate < dateFrom) {
            return false;
        }
        if (dateTo && logDate > dateTo) {
            return false;
        }

        // Search filter
        if (searchFilter && !log.message.toLowerCase().includes(searchFilter) && !log.context.toLowerCase().includes(searchFilter)) {
            return false;
        }

        // Type filter (for different log types)
        if (currentLogType === 'error' && log.level !== 'ERROR') {
            return false;
        }
        if (currentLogType === 'debug' && log.level !== 'DEBUG') {
            return false;
        }

        return true;
    });

    currentPage = 1;
    displaySystemLogs();
}

// Display system logs
function displaySystemLogs() {
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = startIndex + currentLimit;
    const pageData = filteredSystemLogs.slice(startIndex, endIndex);

    const container = document.getElementById('logEntries');

    if (pageData.length === 0) {
        container.innerHTML = `
            <div class="p-6 text-center text-gray-500 dark:text-gray-400">
                Žiadne logy nenájdené
            </div>
        `;
    } else {
        container.innerHTML = pageData.map(log => {
            const levelColor = getLevelColor(log.level);
            return `
                <div class="p-4 border-l-4 ${levelColor.border} ${levelColor.bg} hover:${levelColor.hover} transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded ${levelColor.badge}">
                                    ${log.level}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">${log.timestamp}</span>
                            </div>
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                                ${log.message}
                            </p>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                ${log.context}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-500">
                                ${log.file}:${log.line}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    updatePagination();
}

// Get color scheme for log level
function getLevelColor(level) {
    const colors = {
        'ERROR': {
            border: 'border-red-500',
            bg: 'bg-red-50 dark:bg-red-900/10',
            hover: 'hover:bg-red-100 dark:hover:bg-red-900/20',
            badge: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300'
        },
        'WARNING': {
            border: 'border-yellow-500',
            bg: 'bg-yellow-50 dark:bg-yellow-900/10',
            hover: 'hover:bg-yellow-100 dark:hover:bg-yellow-900/20',
            badge: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300'
        },
        'INFO': {
            border: 'border-blue-500',
            bg: 'bg-blue-50 dark:bg-blue-900/10',
            hover: 'hover:bg-blue-100 dark:hover:bg-blue-900/20',
            badge: 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300'
        },
        'DEBUG': {
            border: 'border-gray-500',
            bg: 'bg-gray-50 dark:bg-gray-800',
            hover: 'hover:bg-gray-100 dark:hover:bg-gray-700',
            badge: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
        }
    };

    return colors[level] || colors['INFO'];
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredSystemLogs.length / currentLimit);
    const startIndex = (currentPage - 1) * currentLimit;
    const endIndex = Math.min(startIndex + currentLimit, filteredSystemLogs.length);

    document.getElementById('showingFrom').textContent = filteredSystemLogs.length > 0 ? startIndex + 1 : 0;
    document.getElementById('showingTo').textContent = endIndex;
    document.getElementById('totalCount').textContent = filteredSystemLogs.length;

    document.getElementById('prevButton').disabled = currentPage <= 1;
    document.getElementById('nextButton').disabled = currentPage >= totalPages;
}

// Pagination functions
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displaySystemLogs();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredSystemLogs.length / currentLimit);
    if (currentPage < totalPages) {
        currentPage++;
        displaySystemLogs();
    }
}

// Clear filters
function clearFilters() {
    document.getElementById('levelFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    document.getElementById('searchFilter').value = '';
    filterSystemLogs();
}

// Refresh system logs
function refreshSystemLogs() {
    if (window.showNotification) {
        window.showNotification('System logy sa obnovujú...', 'info');
    }
    loadSystemLogs();
}

// Clear system logs
function clearSystemLogs() {
    if (confirm('Naozaj chcete vymazať všetky system logy? Táto akcia sa nedá vrátiť späť.')) {
        if (window.showNotification) {
            window.showNotification('Vymazanie system logov nie je implementované', 'warning');
        }
    }
}

// Show error message
function showSystemError(message) {
    const container = document.getElementById('logEntries');
    container.innerHTML = `
        <div class="p-6 text-center text-red-600 dark:text-red-400">
            ${message}
        </div>
    `;
}
</script>
