<?php
// Articles list content for mark layout
$pageTitle = $title ?? 'Articles';
$currentRoute = 'mark-articles';
?>

<!-- Articles Management -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100"><PERSON><PERSON><PERSON><PERSON></h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">S<PERSON><PERSON><PERSON><PERSON> a obsahu</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="refreshArticles()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                O<PERSON><PERSON><PERSON>ť
            </button>
            <a href="/mark/articles/create" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Nový článok
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Celkom článkov</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="articlesGrowth">
                        <span class="loading-skeleton">+-- tento týždeň</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Published Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Publikované</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="publishedArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-blue-600 dark:text-blue-400" id="publishedPercentage">
                        <span class="loading-skeleton">--% z celku</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Draft Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Koncepty</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="draftArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-yellow-600 dark:text-yellow-400">
                        <span class="loading-skeleton">Čakajú na publikáciu</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Archived Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Archivované</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="archivedArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-gray-600 dark:text-gray-400">
                        <span class="loading-skeleton">Neaktívne</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8l4 4 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="glass rounded-xl p-6">
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" 
                       placeholder="Hľadať články..." 
                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                    <option value="">Všetky stavy</option>
                    <option value="published">Publikované</option>
                    <option value="draft">Koncepty</option>
                    <option value="archived">Archivované</option>
                </select>
                <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                    <option value="">Všetky typy</option>
                    <option value="blog_post">Blog príspevok</option>
                    <option value="review">Recenzia</option>
                    <option value="tutorial">Tutoriál</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Articles Table -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Zoznam článkov</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-800/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            <input type="checkbox" class="rounded border-gray-300 dark:border-gray-600">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Názov</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Typ</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Stav</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Autor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Dátum</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Akcie</th>
                    </tr>
                </thead>
                <tbody id="articlesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Articles will be loaded here via JavaScript -->
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <div class="flex flex-col items-center">
                                <svg class="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                <p class="text-lg font-medium">Načítavam články...</p>
                                <p class="text-sm">Prosím čakajte</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje <span class="font-medium">1</span> až <span class="font-medium">10</span> z <span class="font-medium" id="totalArticlesCount">--</span> článkov
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                        Predchádzajúca
                    </button>
                    <button class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700">
                        Ďalšia
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
// Articles management JavaScript
console.log('Articles - Initializing...');

// Load articles data
async function loadArticlesData() {
    console.log('Articles - Loading data...');

    try {
        // Load statistics
        const statsResponse = await fetch('/mark/api/articles/stats');
        if (statsResponse.ok) {
            const stats = await statsResponse.json();
            updateArticleStats(stats);
        }

        // Load articles list (placeholder - would use actual API)
        setTimeout(() => {
            loadArticlesList();
        }, 1000);

    } catch (error) {
        console.error('Articles - Error loading data:', error);
        showMockData();
    }
}

// Update article statistics
function updateArticleStats(stats) {
    console.log('Articles - Updating stats:', stats);

    document.getElementById('totalArticles').textContent = stats.totalArticles || 0;
    document.getElementById('articlesGrowth').textContent = stats.articlesGrowth || '+0 tento týždeň';
    document.getElementById('publishedArticles').textContent = stats.publishedArticles || 0;
    document.getElementById('draftArticles').textContent = stats.draftArticles || 0;
    document.getElementById('archivedArticles').textContent = stats.archivedArticles || 0;
    document.getElementById('publishedPercentage').textContent = (stats.publishedPercentage || 0) + '% z celku';
    document.getElementById('totalArticlesCount').textContent = stats.totalArticles || 0;
}

// Load articles list
function loadArticlesList() {
    const tbody = document.getElementById('articlesTableBody');
    
    // Mock data for now
    const mockArticles = <?php echo json_encode($articles ?? []); ?>;
    
    if (mockArticles.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        <p class="text-lg font-medium">Žiadne články</p>
                        <p class="text-sm">Začnite vytvorením nového článku</p>
                        <a href="/mark/articles/create" class="mt-4 btn btn-primary">Vytvoriť článok</a>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Render articles
    tbody.innerHTML = mockArticles.map(article => `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
            <td class="px-6 py-4">
                <input type="checkbox" class="rounded border-gray-300 dark:border-gray-600" value="${article.id}">
            </td>
            <td class="px-6 py-4">
                <div class="flex items-center">
                    <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            <a href="/mark/articles/${article.id}" class="hover:text-primary-600">${article.title}</a>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">${article.slug}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                    ${article.type_label || 'Neurčený'}
                </span>
            </td>
            <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(article.status)}">
                    ${article.status_label || article.status}
                </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                ${article.author_id || 'Neznámy'}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                ${formatDate(article.created_at)}
            </td>
            <td class="px-6 py-4 text-sm font-medium">
                <div class="flex space-x-2">
                    <a href="/mark/articles/${article.id}" class="text-primary-600 hover:text-primary-900">Zobraziť</a>
                    <a href="/mark/articles/${article.id}/edit" class="text-indigo-600 hover:text-indigo-900">Upraviť</a>
                    <button onclick="deleteArticle('${article.id}')" class="text-red-600 hover:text-red-900">Zmazať</button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Helper functions
function getStatusColor(status) {
    switch(status) {
        case 'published': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
        case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
        case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'Neznámy';
    const date = new Date(dateString);
    return date.toLocaleDateString('sk-SK');
}

function refreshArticles() {
    console.log('Articles - Refreshing...');
    loadArticlesData();
}

function deleteArticle(articleId) {
    if (confirm('Naozaj chcete zmazať tento článok?')) {
        console.log('Articles - Deleting article:', articleId);
        // TODO: Implement delete functionality
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        loadArticlesData();
    }, 500);
});
</script>
