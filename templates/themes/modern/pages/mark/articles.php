<?php
// Articles list content for mark layout
$pageTitle = $title ?? 'Articles';
$currentRoute = 'mark-articles';
$articles = $articles ?? [];
$pagination = $pagination ?? ['total' => 0];
$filters = $filters ?? [];
$error = $error ?? null;
?>

<!-- Articles Management -->
<div class="space-y-6">

    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100"><PERSON><PERSON><PERSON><PERSON></h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON><PERSON><PERSON> a produktov</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="refreshArticles()" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Obnoviť
            </button>
            <a href="/mark/articles/create" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Nový článok
            </a>
        </div>
    </div>

    <?php if ($error): ?>
    <!-- Error Message -->
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Chyba pri načítavaní článkov</h3>
                <p class="mt-1 text-sm text-red-700 dark:text-red-300"><?php echo htmlspecialchars($error); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Celkom článkov</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400" id="articlesGrowth">
                        <span class="loading-skeleton">+-- tento týždeň</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Published Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Publikované</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="publishedArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-blue-600 dark:text-blue-400" id="publishedPercentage">
                        <span class="loading-skeleton">--% z celku</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Draft Articles -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Koncepty</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="draftArticles">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-yellow-600 dark:text-yellow-400">
                        <span class="loading-skeleton">Čakajú na publikáciu</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Products -->
        <div class="glass rounded-xl p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Produkty</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" id="totalProducts">
                        <span class="loading-skeleton">--</span>
                    </p>
                    <p class="text-xs text-purple-600 dark:text-purple-400">
                        <span class="loading-skeleton">Fyzické + digitálne</span>
                    </p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="glass rounded-xl p-6">
        <div class="flex flex-col lg:flex-row gap-4">
            <div class="flex-1">
                <input type="text"
                       id="searchInput"
                       placeholder="Hľadať články..."
                       value="<?php echo htmlspecialchars($filters['search'] ?? ''); ?>"
                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            <div class="flex flex-wrap gap-2">
                <select id="statusFilter" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                    <option value="">Všetky stavy</option>
                    <?php if (isset($filters['available_statuses'])): ?>
                        <?php foreach ($filters['available_statuses'] as $status): ?>
                            <option value="<?php echo htmlspecialchars($status['value']); ?>"
                                    <?php echo ($filters['status'] ?? '') === $status['value'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($status['label']); ?> (<?php echo $status['count']; ?>)
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>

                <select id="typeFilter" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500">
                    <option value="">Všetky typy</option>
                    <?php if (isset($filters['available_types'])): ?>
                        <?php foreach ($filters['available_types'] as $type): ?>
                            <option value="<?php echo htmlspecialchars($type['value']); ?>"
                                    <?php echo ($filters['type'] ?? '') === $type['value'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($type['label']); ?> (<?php echo $type['count']; ?>)
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>

                <button onclick="applyFilters()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    Filtrovať
                </button>

                <button onclick="clearFilters()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                    Vymazať
                </button>
            </div>
        </div>
    </div>

    <!-- Articles Table -->
    <div class="glass rounded-xl overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Zoznam článkov</h3>
            <div class="flex items-center space-x-2">
                <button onclick="toggleBulkActions()" id="bulkToggle" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    Hromadné operácie
                </button>
            </div>
        </div>

        <!-- Bulk Actions Bar -->
        <div id="bulkActionsBar" class="hidden px-6 py-3 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-blue-700 dark:text-blue-300">
                        <span id="selectedCount">0</span> článkov označených
                    </span>
                    <button onclick="selectAll()" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                        Označiť všetky
                    </button>
                    <button onclick="deselectAll()" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                        Zrušiť výber
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="bulkAction('publish')" class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                        Publikovať
                    </button>
                    <button onclick="bulkAction('unpublish')" class="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700">
                        Zrušiť publikáciu
                    </button>
                    <button onclick="bulkAction('delete')" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                        Zmazať
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-800/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            <input type="checkbox" id="selectAllCheckbox" class="rounded border-gray-300 dark:border-gray-600" onchange="toggleSelectAll()">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Článok</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Typ</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Stav</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cena</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Dátum</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Akcie</th>
                    </tr>
                </thead>
                <tbody id="articlesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php if (empty($articles)): ?>
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <div class="flex flex-col items-center">
                                <svg class="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                <p class="text-lg font-medium">Žiadne články</p>
                                <p class="text-sm">Začnite vytvorením nového článku</p>
                                <a href="/mark/articles/create" class="mt-4 btn btn-primary">Vytvoriť článok</a>
                            </div>
                        </td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($articles as $article): ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                            <td class="px-6 py-4">
                                <input type="checkbox" class="article-checkbox rounded border-gray-300 dark:border-gray-600"
                                       value="<?php echo htmlspecialchars($article['id']); ?>" onchange="updateBulkActions()">
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <?php if (!empty($article['image'])): ?>
                                    <img src="<?php echo htmlspecialchars($article['image']); ?>"
                                         alt="<?php echo htmlspecialchars($article['title']); ?>"
                                         class="w-12 h-12 rounded-lg object-cover">
                                    <?php else: ?>
                                    <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                    <?php endif; ?>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-2">
                                            <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>"
                                               class="text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 truncate">
                                                <?php echo htmlspecialchars($article['title']); ?>
                                            </a>
                                            <?php if ($article['is_featured']): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                                ⭐ Featured
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                            <?php echo htmlspecialchars($article['slug']); ?>
                                        </div>
                                        <?php if (!empty($article['excerpt'])): ?>
                                        <div class="text-xs text-gray-400 dark:text-gray-500 mt-1 line-clamp-2">
                                            <?php echo htmlspecialchars(substr($article['excerpt'], 0, 100)); ?>...
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-100 text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-800 dark:bg-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-900/20 dark:text-<?php echo $article['type'] === 'product_physical' ? 'green' : ($article['type'] === 'product_digital' ? 'purple' : 'blue'); ?>-400">
                                    <?php echo htmlspecialchars($article['type_label'] ?? $article['type']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-100 text-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-800 dark:bg-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-900/20 dark:text-<?php echo $article['status'] === 'published' ? 'green' : ($article['status'] === 'draft' ? 'yellow' : 'gray'); ?>-400">
                                    <?php echo htmlspecialchars($article['status_label'] ?? $article['status']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                <?php if ($article['is_product'] && !empty($article['price'])): ?>
                                    <div class="font-medium"><?php echo htmlspecialchars($article['price']['formatted']); ?></div>
                                    <?php if (!empty($article['sku'])): ?>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">SKU: <?php echo htmlspecialchars($article['sku']); ?></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-gray-400">—</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                <div><?php echo date('d.m.Y', strtotime($article['created_at'])); ?></div>
                                <div class="text-xs"><?php echo date('H:i', strtotime($article['created_at'])); ?></div>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>"
                                       class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1 rounded hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors"
                                       title="Zobraziť článok">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                    <a href="/mark/articles/<?php echo htmlspecialchars($article['id']); ?>/edit"
                                       class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors"
                                       title="Upraviť článok">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <button onclick="toggleFeatured('<?php echo htmlspecialchars($article['id']); ?>')"
                                            class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 p-1 rounded hover:bg-yellow-50 dark:hover:bg-yellow-900/20 transition-colors"
                                            title="<?php echo $article['is_featured'] ? 'Zrušiť featured' : 'Označiť ako featured'; ?>">
                                        <?php if ($article['is_featured']): ?>
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                                            </svg>
                                        <?php else: ?>
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                            </svg>
                                        <?php endif; ?>
                                    </button>
                                    <button onclick="deleteArticle('<?php echo htmlspecialchars($article['id']); ?>')"
                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                                            title="Zmazať článok">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($pagination['total'] > 0): ?>
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    Zobrazuje <span class="font-medium"><?php echo $pagination['offset'] + 1; ?></span>
                    až <span class="font-medium"><?php echo min($pagination['offset'] + $pagination['limit'], $pagination['total']); ?></span>
                    z <span class="font-medium"><?php echo $pagination['total']; ?></span> článkov
                </div>
                <div class="flex space-x-2">
                    <?php if ($pagination['has_prev']): ?>
                    <a href="?offset=<?php echo max(0, $pagination['offset'] - $pagination['limit']); ?>&limit=<?php echo $pagination['limit']; ?><?php echo !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : ''; ?><?php echo !empty($filters['type']) ? '&type=' . urlencode($filters['type']) : ''; ?><?php echo !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : ''; ?>"
                       class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700">
                        Predchádzajúca
                    </a>
                    <?php else: ?>
                    <span class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded opacity-50 cursor-not-allowed">
                        Predchádzajúca
                    </span>
                    <?php endif; ?>

                    <?php if ($pagination['has_next']): ?>
                    <a href="?offset=<?php echo $pagination['offset'] + $pagination['limit']; ?>&limit=<?php echo $pagination['limit']; ?><?php echo !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : ''; ?><?php echo !empty($filters['type']) ? '&type=' . urlencode($filters['type']) : ''; ?><?php echo !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : ''; ?>"
                       class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700">
                        Ďalšia
                    </a>
                    <?php else: ?>
                    <span class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded opacity-50 cursor-not-allowed">
                        Ďalšia
                    </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

</div>

<script>
// Articles management JavaScript
console.log('Articles - Initializing...');

// Load articles data and statistics
async function loadArticlesData() {
    console.log('Articles - Loading data...');

    try {
        // Load statistics
        const statsResponse = await fetch('/mark/api/dashboard/stats');
        if (statsResponse.ok) {
            const stats = await statsResponse.json();
            updateArticleStats(stats);
        }
    } catch (error) {
        console.error('Articles - Error loading data:', error);
    }
}

// Update article statistics
function updateArticleStats(stats) {
    console.log('Articles - Updating stats:', stats);

    document.getElementById('totalArticles').textContent = stats.totalArticles || 0;
    document.getElementById('articlesGrowth').textContent = stats.articlesGrowth || '+0 tento týždeň';
    document.getElementById('publishedArticles').textContent = stats.publishedArticles || 0;
    document.getElementById('draftArticles').textContent = stats.draftArticles || 0;
    document.getElementById('totalProducts').textContent = stats.totalProducts || 0;

    const publishedPercentage = stats.totalArticles > 0 ? Math.round((stats.publishedArticles / stats.totalArticles) * 100) : 0;
    document.getElementById('publishedPercentage').textContent = publishedPercentage + '% z celku';
}

// Filter and search functions
function applyFilters() {
    const search = document.getElementById('searchInput').value;
    const status = document.getElementById('statusFilter').value;
    const type = document.getElementById('typeFilter').value;

    const params = new URLSearchParams();
    if (search) params.set('search', search);
    if (status) params.set('status', status);
    if (type) params.set('type', type);

    window.location.href = '/mark/articles?' + params.toString();
}

function clearFilters() {
    window.location.href = '/mark/articles';
}

function refreshArticles() {
    window.location.reload();
}

// Bulk actions
let bulkActionsVisible = false;

function toggleBulkActions() {
    bulkActionsVisible = !bulkActionsVisible;
    const bar = document.getElementById('bulkActionsBar');
    const toggle = document.getElementById('bulkToggle');

    if (bulkActionsVisible) {
        bar.classList.remove('hidden');
        toggle.textContent = 'Skryť hromadné operácie';
    } else {
        bar.classList.add('hidden');
        toggle.textContent = 'Hromadné operácie';
        deselectAll();
    }
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.article-checkbox:checked');
    const count = checkboxes.length;

    document.getElementById('selectedCount').textContent = count;

    if (count > 0 && !bulkActionsVisible) {
        toggleBulkActions();
    }
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.article-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
    updateBulkActions();
}

function deselectAll() {
    const checkboxes = document.querySelectorAll('.article-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateBulkActions();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.article-checkbox');

    checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
    updateBulkActions();
}

async function bulkAction(action) {
    const checkboxes = document.querySelectorAll('.article-checkbox:checked');
    const articleIds = Array.from(checkboxes).map(cb => cb.value);

    if (articleIds.length === 0) {
        alert('Vyberte aspoň jeden článok');
        return;
    }

    const actionNames = {
        'publish': 'publikovať',
        'unpublish': 'zrušiť publikáciu',
        'delete': 'zmazať'
    };

    if (!confirm(`Naozaj chcete ${actionNames[action]} ${articleIds.length} článkov?`)) {
        return;
    }

    try {
        const response = await fetch('/mark/articles/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                article_ids: articleIds
            })
        });

        const result = await response.json();

        if (response.ok) {
            alert(result.message);
            window.location.reload();
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Bulk action error:', error);
        alert('Nastala chyba pri vykonávaní operácie');
    }
}

// Individual article actions
async function deleteArticle(articleId) {
    if (!confirm('Naozaj chcete zmazať tento článok?')) {
        return;
    }

    try {
        const response = await fetch(`/mark/articles/${articleId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
            alert(result.message);
            window.location.reload();
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Delete error:', error);
        alert('Nastala chyba pri mazaní článku');
    }
}

async function toggleFeatured(articleId) {
    try {
        const response = await fetch(`/mark/articles/${articleId}/toggle-featured`, {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            window.location.reload();
        } else {
            alert('Chyba: ' + result.message);
        }
    } catch (error) {
        console.error('Toggle featured error:', error);
        alert('Nastala chyba pri zmene featured statusu');
    }
}

// Search on Enter key
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        applyFilters();
    }
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        loadArticlesData();
    }, 500);
});
</script>