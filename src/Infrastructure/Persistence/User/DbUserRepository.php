<?php

declare(strict_types = 1);

namespace App\Infrastructure\Persistence\User;

use App\Infrastructure\Persistence\BaseRepository;
use App\Module\User\Domain\Entity\User;
use App\Module\User\Domain\Repository\UserRepositoryInterface;
use App\Module\User\Domain\ValueObject\UserId;
use Cake\Database\Connection as CakeConnection;

class DbUserRepository extends BaseRepository implements UserRepositoryInterface
{
    public function __construct(
        CakeConnection $connection,
    ) {
        parent::__construct($connection);
    }

    public function find(UserId $id): ?User
    {
        $statement = $this->connection->execute(
            'SELECT * FROM users WHERE id = :id',
            ['id' => $id->toRfc4122()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findByEmail(string $email): ?User
    {
        $statement = $this->connection->execute(
            'SELECT * FROM users WHERE email = :email',
            ['email' => $email]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findByUsername(string $username): ?User
    {
        $statement = $this->connection->execute(
            'SELECT * FROM users WHERE username = :username',
            ['username' => $username]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    /**
     * @param int $limit
     * @param int $offset
     *
     * @return User[]
     */
    public function findAll(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM users ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        $results = $statement->fetchAll('assoc');

        $users = [];
        foreach ($results as $result) {
            $users[] = $this->mapToEntity($result);
        }

        return $users;
    }

    public function save(object $entity): void
    {
        if (!$entity instanceof User) {
            throw new \InvalidArgumentException('Entity must be instance of User');
        }
        $this->saveUser($entity);
    }

    public function saveUser(User $user): void
    {
        $data = [
            'id' => $user->getId()->toRfc4122(),
            'email' => $user->getEmail(),
            'username' => $user->getUsername(),
            'password' => $user->getPasswordHash(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'is_active' => $user->isActive() ? 1 : 0,
            'created_at' => $user->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $user->getUpdatedAt() ? $user->getUpdatedAt()->format('Y-m-d H:i:s') : null,
        ];

        $existingUser = $this->find($user->getId());

        if ($existingUser === null) {
            // Insert new user
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $this->connection->execute(
                "INSERT INTO users ($columns) VALUES ($placeholders)",
                $data
            );
        } else {
            // Update existing user
            unset($data['id']);
            unset($data['created_at']);

            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "$key = :$key";
            }
            $setClause = implode(', ', $setClause);

            $this->connection->execute(
                "UPDATE users SET $setClause WHERE id = :id",
                array_merge($data, ['id' => $user->getId()->toRfc4122()])
            );
        }
    }

    public function delete(User $user): void
    {
        $this->connection->execute(
            'DELETE FROM users WHERE id = :id',
            ['id' => $user->getId()->toRfc4122()]
        );
    }

    public function count(): int
    {
        $statement = $this->connection->execute('SELECT COUNT(*) as count FROM users');
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    /**
     * Count active users
     *
     * @return int
     */
    public function countActive(): int
    {
        $statement = $this->connection->execute('SELECT COUNT(*) as count FROM users WHERE is_active = 1');
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    /**
     * Find user by ID (string version for UserListService)
     *
     * @param string $id
     * @return User|null
     */
    public function findById(string $id): ?User
    {
        return $this->find(UserId::fromString($id));
    }

    /**
     * Create new user from array data
     *
     * @param array $userData
     * @return string User ID
     */
    public function create(array $userData): string
    {
        $userId = UserId::generate();

        $user = new User(
            $userId,
            $userData['email'],
            $userData['username'],
            $userData['password'], // Already hashed in service
            $userData['first_name'] ?? null,
            $userData['last_name'] ?? null,
            (bool)($userData['is_active'] ?? true),
            $userData['created_at'] ?? new \DateTimeImmutable(),
            null
        );

        $this->save($user);

        return $userId->toRfc4122();
    }

    /**
     * Update user by ID
     *
     * @param string $userId
     * @param array $userData
     * @return bool
     */
    public function update(string $userId, array $userData): bool
    {
        $user = $this->findById($userId);
        if (!$user) {
            return false;
        }

        // Update user properties
        if (isset($userData['email']) || isset($userData['username']) ||
            isset($userData['first_name']) || isset($userData['last_name'])) {
            $user->updateProfile(
                $userData['email'] ?? $user->getEmail(),
                $userData['username'] ?? $user->getUsername(),
                $userData['first_name'] ?? $user->getFirstName(),
                $userData['last_name'] ?? $user->getLastName()
            );
        }

        // Update password if provided
        if (isset($userData['password'])) {
            $user->changePassword($userData['password']);
        }

        // Update active status
        if (isset($userData['is_active'])) {
            if ($userData['is_active']) {
                $user->activate();
            } else {
                $user->deactivate();
            }
        }

        $this->save($user);

        return true;
    }

    /**
     * Delete user by ID
     *
     * @param string $userId
     * @return bool
     */
    public function deleteById(string $userId): bool
    {
        $user = $this->findById($userId);
        if (!$user) {
            return false;
        }

        $this->delete($user);

        return true;
    }

    private function mapToEntity(array $row): User
    {
        return new User(
            UserId::fromString($row['id']),
            $row['email'],
            $row['username'],
            $row['password'], // This will be stored as password_hash in the User entity
            $row['first_name'],
            $row['last_name'],
            (bool)$row['is_active'],
            new \DateTimeImmutable($row['created_at']),
            $row['updated_at'] ? new \DateTimeImmutable($row['updated_at']) : null
        );
    }
}
