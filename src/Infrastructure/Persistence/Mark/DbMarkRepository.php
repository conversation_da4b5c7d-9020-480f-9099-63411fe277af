<?php

declare(strict_types = 1);

namespace App\Infrastructure\Persistence\Mark;

use App\Infrastructure\Persistence\BaseRepository;
use App\Module\Mark\Domain\Entity\Mark;
use App\Module\Mark\Domain\Repository\MarkRepositoryInterface;
use App\Module\Mark\Domain\ValueObject\MarkId;
use App\Module\User\Domain\ValueObject\UserId;
use Cake\Database\Connection as CakeConnection;

class DbMarkRepository extends BaseRepository implements MarkRepositoryInterface
{
    public function __construct(
        CakeConnection $connection,
    ) {
        parent::__construct($connection);
    }

    public function find(MarkId $id): ?Mark
    {
        $statement = $this->connection->execute(
            'SELECT * FROM marks WHERE id = :id',
            ['id' => $id->toRfc4122()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    /**
     * @param UserId $userId
     * @param int $limit
     * @param int $offset
     *
     * @return Mark[]
     */
    public function findByUser(UserId $userId, int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM marks WHERE user_id = :user_id ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'user_id' => $userId->toRfc4122(),
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        $results = $statement->fetchAll('assoc');

        $marks = [];
        foreach ($results as $result) {
            $marks[] = $this->mapToEntity($result);
        }

        return $marks;
    }

    /**
     * @param int $limit
     * @param int $offset
     *
     * @return Mark[]
     */
    public function findPublic(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM marks WHERE is_public = 1 ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        $results = $statement->fetchAll('assoc');

        $marks = [];
        foreach ($results as $result) {
            $marks[] = $this->mapToEntity($result);
        }

        return $marks;
    }

    public function save(object $entity): void
    {
        if (!$entity instanceof Mark) {
            throw new \InvalidArgumentException('Entity must be instance of Mark');
        }
        $this->saveMark($entity);
    }

    public function saveMark(Mark $mark): void
    {
        $data = [
            'id' => $mark->getId()->toRfc4122(),
            'user_id' => $mark->getUserId()->toRfc4122(),
            'title' => $mark->getTitle(),
            'content' => $mark->getContent(),
            'is_public' => $mark->isPublic() ? 1 : 0,
            'created_at' => $mark->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $mark->getUpdatedAt() ? $mark->getUpdatedAt()->format('Y-m-d H:i:s') : null,
        ];

        $existing = $this->find($mark->getId());

        if ($existing === null) {
            // Insert new mark
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $this->connection->execute(
                "INSERT INTO marks ($columns) VALUES ($placeholders)",
                $data
            );
        } else {
            // Update existing mark
            unset($data['id']);
            unset($data['created_at']);

            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "$key = :$key";
            }
            $setClause = implode(', ', $setClause);

            $this->connection->execute(
                "UPDATE marks SET $setClause WHERE id = :id",
                array_merge($data, ['id' => $mark->getId()->toRfc4122()])
            );
        }
    }

    public function delete(object $entity): void
    {
        if (!$entity instanceof Mark) {
            throw new \InvalidArgumentException('Entity must be instance of Mark');
        }
        $this->deleteMark($entity);
    }

    public function deleteMark(Mark $mark): void
    {
        $this->connection->execute(
            'DELETE FROM marks WHERE id = :id',
            ['id' => $mark->getId()->toRfc4122()]
        );
    }

    public function countByUser(UserId $userId): int
    {
        $statement = $this->connection->execute(
            'SELECT COUNT(*) as count FROM marks WHERE user_id = :user_id',
            ['user_id' => $userId->toRfc4122()]
        );
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    public function countPublic(): int
    {
        $statement = $this->connection->execute('SELECT COUNT(*) as count FROM marks WHERE is_public = 1');
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    private function mapToEntity(array $row): Mark
    {
        return new Mark(
            MarkId::fromString($row['id']),
            UserId::fromString($row['user_id']),
            $row['title'],
            $row['content'],
            (bool)$row['is_public'],
            new \DateTimeImmutable($row['created_at']),
            $row['updated_at'] ? new \DateTimeImmutable($row['updated_at']) : null
        );
    }
}
