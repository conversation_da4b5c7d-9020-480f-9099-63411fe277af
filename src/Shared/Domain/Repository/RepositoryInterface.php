<?php

declare(strict_types = 1);

namespace App\Shared\Domain\Repository;

/**
 * Base repository interface for common CRUD operations
 * 
 * @template T The entity type
 * @template ID The ID value object type
 */
interface RepositoryInterface
{
    /**
     * Find entity by ID
     * 
     * @param ID $id
     * @return T|null
     */
    public function find($id): ?object;

    /**
     * Save entity (insert or update)
     * 
     * @param T $entity
     * @return void
     */
    public function save(object $entity): void;

    /**
     * Delete entity
     * 
     * @param T $entity
     * @return void
     */
    public function delete(object $entity): void;

    /**
     * Count total entities
     * 
     * @return int
     */
    public function count(): int;
}
