<?php

declare(strict_types = 1);

namespace App\Shared\Domain\Enum;

enum ProductStatus: string
{
    case DRAFT = 'draft';
    case PUBLISHED = 'published';
    case ARCHIVED = 'archived';

    public function getLabel(): string
    {
        return match($this) {
            self::DRAFT => 'Draft',
            self::PUBLISHED => 'Published',
            self::ARCHIVED => 'Archived',
        };
    }

    public function isPublic(): bool
    {
        return $this === self::PUBLISHED;
    }
}