<?php

declare(strict_types = 1);

namespace App\Shared\Domain\ValueObject;

use Ramsey\Uuid\Uuid;

abstract class AbstractUuidId
{
    private function __construct(
        private readonly string $value,
    ) {
        if (!Uuid::isValid($this->value)) {
            throw new \InvalidArgumentException(sprintf('Invalid %s format', static::class));
        }
    }

    public static function fromString(string $value): static
    {
        return new static($value);
    }

    public static function generate(): static
    {
        return new static(Uuid::uuid4()->toString());
    }

    public function toRfc4122(): string
    {
        return $this->value;
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
