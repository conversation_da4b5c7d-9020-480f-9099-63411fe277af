<?php

declare(strict_types = 1);

namespace App\Module\User\Infrastructure\Repository;

use App\Infrastructure\Factory\QueryFactory;
use App\Module\User\Domain\Repository\UserRepositoryInterface;
use App\Module\User\Domain\Entity\User;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * Adapter that provides QueryFactory-based operations for the DDD UserRepository
 * This bridges the gap between the DDD approach (UUID-based) and CRUD approach (int-based)
 */
final readonly class UserRepositoryAdapter
{
    public function __construct(
        private QueryFactory $queryFactory,
        private UserRepositoryInterface $userRepository,
    ) {
    }

    /**
     * Create user using raw data (CRUD style)
     * 
     * @param array<string, mixed> $userRow
     * @return int lastInsertId
     */
    public function insertUser(array $userRow): int
    {
        return (int)$this->queryFactory->insertQueryWithData($userRow)->into('user')->execute()->lastInsertId();
    }

    /**
     * Update user using raw data (CRUD style)
     * 
     * @param int $userId
     * @param array<string, int|string|null> $userValues
     * @return bool
     */
    public function updateUser(int $userId, array $userValues): bool
    {
        $query = $this->queryFactory->updateQuery()->update('user')->set($userValues)->where(['id' => $userId]);
        return $query->execute()->rowCount() > 0;
    }

    /**
     * Soft delete user (CRUD style)
     * 
     * @param int $userId
     * @return bool
     */
    public function deleteUserById(int $userId): bool
    {
        $query = $this->queryFactory->softDeleteQuery('user')->where(['id' => $userId]);
        return $query->execute()->rowCount() > 0;
    }

    /**
     * Find user by int ID and return as array (CRUD style)
     * 
     * @param int $id
     * @return array<string, mixed>
     */
    public function findUserById(int $id): array
    {
        $query = $this->queryFactory->selectQuery()->select([
            'id',
            'first_name',
            'last_name',
            'email',
            'updated_at',
            'created_at',
        ])->from('user')->where(
            ['deleted_at IS' => null, 'id' => $id]
        );

        return $query->execute()->fetch('assoc') ?: [];
    }

    /**
     * Find all users (CRUD style)
     * 
     * @return array<array<string, mixed>>
     */
    public function findAllUsers(): array
    {
        $query = $this->queryFactory->selectQuery()->select([
            'id',
            'first_name',
            'last_name',
            'email',
            'updated_at',
            'created_at',
        ])->from('user')->where(['deleted_at IS' => null]);

        return $query->execute()->fetchAll('assoc') ?: [];
    }

    /**
     * Check if user with email exists (validation style)
     * 
     * @param string $email
     * @param int|null $userIdToExclude
     * @return bool
     */
    public function userWithEmailAlreadyExists(string $email, ?int $userIdToExclude = null): bool
    {
        $query = $this->queryFactory->selectQuery()->select(['id'])->from('user')->andWhere(
            ['deleted_at IS' => null, 'email' => $email]
        );

        if ($userIdToExclude !== null) {
            $query->andWhere(['id !=' => $userIdToExclude]);
        }

        return $query->execute()->fetch('assoc') !== false;
    }

    /**
     * Bridge method: Convert int ID to DDD User entity
     * 
     * @param int $id
     * @return User|null
     */
    public function findDomainUserById(int $id): ?User
    {
        // This would require a mapping between int ID and UUID
        // For now, we'll keep the systems separate
        throw new \BadMethodCallException('Bridge between int ID and UUID not implemented yet');
    }
}
