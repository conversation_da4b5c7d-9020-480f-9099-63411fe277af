<?php

declare(strict_types = 1);

namespace App\Module\User\Domain\Repository;

use App\Module\User\Domain\Entity\User;
use App\Module\User\Domain\ValueObject\UserId;
use App\Shared\Domain\Repository\RepositoryInterface;

/**
 * @extends RepositoryInterface<User, UserId>
 */
interface UserRepositoryInterface extends RepositoryInterface
{
    public function find(UserId $id): ?User;

    public function findByEmail(string $email): ?User;

    public function findByUsername(string $username): ?User;

    /**
     * @param int $limit
     * @param int $offset
     *
     * @return User[]
     */
    public function findAll(int $limit = 10, int $offset = 0): array;

    public function save(User $user): void;

    public function delete(User $user): void;

    public function count(): int;
}
