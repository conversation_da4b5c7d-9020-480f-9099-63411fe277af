<?php

declare(strict_types = 1);

namespace App\Module\Mark\Domain\Repository;

use App\Module\Mark\Domain\Entity\Mark;
use App\Module\Mark\Domain\ValueObject\MarkId;
use App\Module\User\Domain\ValueObject\UserId;
use App\Shared\Domain\Repository\RepositoryInterface;

/**
 * @extends RepositoryInterface<Mark, MarkId>
 */
interface MarkRepositoryInterface extends RepositoryInterface
{
    public function find(MarkId $id): ?Mark;

    /**
     * @param UserId $userId
     * @param int $limit
     * @param int $offset
     *
     * @return Mark[]
     */
    public function findByUser(UserId $userId, int $limit = 10, int $offset = 0): array;

    /**
     * @param int $limit
     * @param int $offset
     *
     * @return Mark[]
     */
    public function findPublic(int $limit = 10, int $offset = 0): array;

    public function save(object $entity): void;

    public function delete(object $entity): void;

    public function count(): int;

    // Specific methods with proper types
    public function saveMark(Mark $mark): void;

    public function deleteMark(Mark $mark): void;

    public function countByUser(UserId $userId): int;

    public function countPublic(): int;
}
