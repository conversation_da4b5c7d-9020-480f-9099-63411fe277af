<?php

declare(strict_types = 1);

namespace App\Module\Article\Domain\Entity;

use App\Module\Article\Domain\Enum\ArticleType;
use App\Shared\Domain\Enum\Currency;
use App\Shared\Domain\Enum\ProductStatus;
use App\Shared\Domain\Enum\ProductType;

final class Article
{
    public function __construct(
        private string $id,
        private string $title,
        private string $slug,
        private string $excerpt,
        private ?string $content,
        private ?string $image,
        private ?string $authorId,
        private ProductType $productType,
        private ?ArticleType $articleType,
        private ?string $productSubtype,
        private ?string $sku,
        private ?float $price,
        private Currency $currency,
        private ProductStatus $status,
        private bool $isFeatured,
        private bool $isPublic,
        private ?string $metaTitle,
        private ?string $metaDescription,
        private ?string $metaKeywords,
        private ?\DateTimeImmutable $publishedAt,
        private \DateTimeImmutable $createdAt,
        private ?\DateTimeImmutable $updatedAt,
        private ?\DateTimeImmutable $deletedAt,
    ) {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getExcerpt(): string
    {
        return $this->excerpt;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function getAuthorId(): ?string
    {
        return $this->authorId;
    }

    public function getProductType(): ProductType
    {
        return $this->productType;
    }

    public function getArticleType(): ?ArticleType
    {
        return $this->articleType;
    }

    public function getProductSubtype(): ?string
    {
        return $this->productSubtype;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function getStatus(): ProductStatus
    {
        return $this->status;
    }

    public function isFeatured(): bool
    {
        return $this->isFeatured;
    }

    public function isPublic(): bool
    {
        return $this->isPublic;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getPublishedAt(): ?\DateTimeImmutable
    {
        return $this->publishedAt;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getDeletedAt(): ?\DateTimeImmutable
    {
        return $this->deletedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'excerpt' => $this->excerpt,
            'content' => $this->content,
            'image' => $this->image,
            'author_id' => $this->authorId,
            'product_type' => $this->productType->value,
            'article_type' => $this->articleType?->value,
            'product_subtype' => $this->productSubtype,
            'sku' => $this->sku,
            'price' => $this->price,
            'currency' => $this->currency->value,
            'status' => $this->status->value,
            'is_featured' => $this->isFeatured,
            'is_public' => $this->isPublic,
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'published_at' => $this->publishedAt?->format('Y-m-d H:i:s'),
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deletedAt?->format('Y-m-d H:i:s'),
            // Backward compatibility - use published_at if available, otherwise created_at
            'date' => ($this->publishedAt ?? $this->createdAt)->format('Y-m-d'),
        ];
    }
}
