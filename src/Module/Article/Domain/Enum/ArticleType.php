<?php

declare(strict_types = 1);

namespace App\Module\Article\Domain\Enum;

enum ArticleType: string
{
    case BLOG_POST = 'blog_post';
    case REVIEW = 'review';
    case TUTORIAL = 'tutorial';

    public function getLabel(): string
    {
        return match($this) {
            self::BLOG_POST => 'Blog Post',
            self::REVIEW => 'Review',
            self::TUTORIAL => 'Tutorial',
        };
    }

    public function getPrefix(): string
    {
        return match($this) {
            self::BLOG_POST => 'BLOG',
            self::REVIEW => 'REVIE',
            self::TUTORIAL => 'TUTOR',
        };
    }
}
