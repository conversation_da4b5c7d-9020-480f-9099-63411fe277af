<?php

declare(strict_types = 1);

namespace App\Module\Article\Domain\ValueObject;

use App\Shared\Domain\Enum\ProductType;

final class SKU
{
    private function __construct(
        private string $value,
    ) {
        $this->validate($value);
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function generate(
        ProductType $productType,
        string $subtype,
        string $uniqueCode,
    ): self {
        $typePrefix = $productType->getPrefix();
        $subtypePrefix = self::getSubtypePrefix($subtype);
        $value = sprintf('%s-%s-%s', $typePrefix, $subtypePrefix, $uniqueCode);

        return new self($value);
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    private function validate(string $value): void
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('SKU cannot be empty');
        }

        if (strlen($value) > 100) {
            throw new \InvalidArgumentException('SKU cannot be longer than 100 characters');
        }

        // Basic pattern validation: TYPE-SUBTYPE-CODE
        if (!preg_match('/^[A-Z]{3}-[A-Z0-9]{1,5}-[A-Z0-9]+$/i', $value)) {
            throw new \InvalidArgumentException(
                'SKU must follow pattern: TYPE-SUBTYPE-CODE (e.g., ART-BLOG-12345)'
            );
        }
    }

    private static function getSubtypePrefix(string $subtype): string
    {
        // Convert subtype to uppercase and take first 5 characters
        $prefix = strtoupper($subtype);

        return substr($prefix, 0, 5);
    }

    public function equals(SKU $other): bool
    {
        return $this->value === $other->value;
    }
}
