<?php

declare(strict_types = 1);

namespace App\Module\Article\Domain\Repository;

use App\Module\Article\Domain\Entity\Article;
use App\Core\Domain\ValueObject\ArticleId;
use App\Core\Domain\Repository\RepositoryInterface;

/**
 * @extends RepositoryInterface<Article, ArticleId>
 */
interface ArticleRepository extends RepositoryInterface
{
    /**
     * @param int $limit
     *
     * @return Article[]
     */
    public function findLatest(int $limit = 10): array;

    public function find(ArticleId $id): ?Article;

    public function findById(string $id): ?Article;

    public function findBySlug(string $slug): ?Article;

    public function save(object $entity): void;

    public function delete(object $entity): void;

    public function count(): int;

    // Specific methods with proper types
    public function saveArticle(Article $article): void;

    public function deleteArticle(Article $article): void;
}
