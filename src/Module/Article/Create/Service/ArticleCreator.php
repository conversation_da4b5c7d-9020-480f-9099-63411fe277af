<?php

declare(strict_types = 1);

namespace App\Module\Article\Create\Service;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Repository\ArticleRepositoryInterface;
use App\Module\Article\Domain\ValueObject\ArticleId;
use App\Module\Article\Domain\ValueObject\Price;
use App\Module\Article\Domain\ValueObject\SKU;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Enum\ArticleStatus;
use App\Module\Article\Domain\Enum\DigitalDeliveryType;
use App\Core\Domain\Enum\Currency;

final readonly class ArticleCreator
{
    public function __construct(
        private ArticleRepositoryInterface $articleRepository,
    ) {
    }

    /**
     * Create new article
     */
    public function createArticle(array $data): string
    {
        // Validate required fields
        $this->validateRequiredFields($data);

        // Generate ID and slug
        $articleId = ArticleId::generate();
        $slug = $this->generateSlug($data['title']);

        // Parse enums
        $type = ArticleType::from($data['article_type']);
        $status = ArticleStatus::from($data['status'] ?? 'draft');

        // Validate type-specific requirements
        $this->validateTypeRequirements($type, $data);

        // Create price if provided
        $price = null;
        if ($type->requiresPrice() && isset($data['price']) && $data['price'] > 0) {
            $currency = Currency::from($data['currency'] ?? 'EUR');
            $price = Price::create((float)$data['price'], $currency);
        }

        // Create SKU if provided or generate for products
        $sku = null;
        if ($type->requiresSku()) {
            if (!empty($data['sku'])) {
                $sku = SKU::fromString($data['sku']);
                
                // Check SKU uniqueness
                if ($this->articleRepository->skuExists($sku)) {
                    throw new \InvalidArgumentException('SKU already exists');
                }
            } else {
                // Auto-generate SKU
                $sku = SKU::generate($type->value, $data['title']);
                
                // Ensure generated SKU is unique
                $attempts = 0;
                while ($this->articleRepository->skuExists($sku) && $attempts < 10) {
                    $sku = SKU::generate($type->value, $data['title']);
                    $attempts++;
                }
                
                if ($attempts >= 10) {
                    throw new \RuntimeException('Could not generate unique SKU');
                }
            }
        }

        // Handle digital delivery type
        $digitalDeliveryType = null;
        if ($type === ArticleType::PRODUCT_DIGITAL && !empty($data['digital_delivery_type'])) {
            $digitalDeliveryType = DigitalDeliveryType::from($data['digital_delivery_type']);
        }

        // Handle inventory for physical products
        $inventory = null;
        if ($type === ArticleType::PRODUCT_PHYSICAL && isset($data['inventory'])) {
            $inventory = max(0, (int)$data['inventory']);
        }

        // Handle publishing
        $publishedAt = null;
        $isPublic = (bool)($data['is_public'] ?? true);
        if ($status === ArticleStatus::PUBLISHED) {
            $publishedAt = new \DateTimeImmutable();
        }

        // Create article entity
        $article = new Article(
            $articleId,
            trim($data['title']),
            $slug,
            $type,
            $status,
            trim($data['content']),
            !empty($data['excerpt']) ? trim($data['excerpt']) : null,
            !empty($data['image']) ? trim($data['image']) : null,
            !empty($data['author_id']) ? trim($data['author_id']) : null,
            $price,
            $sku,
            $inventory,
            $digitalDeliveryType,
            !empty($data['digital_file']) ? trim($data['digital_file']) : null,
            !empty($data['meta_title']) ? trim($data['meta_title']) : null,
            !empty($data['meta_description']) ? trim($data['meta_description']) : null,
            !empty($data['meta_keywords']) ? trim($data['meta_keywords']) : null,
            (bool)($data['is_featured'] ?? false),
            $isPublic,
            $publishedAt,
            null, // scheduled_at
        );

        // Save article
        $this->articleRepository->save($article);

        return $articleId->toRfc4122();
    }

    /**
     * Validate required fields
     */
    private function validateRequiredFields(array $data): void
    {
        $required = ['title', 'content', 'article_type'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \InvalidArgumentException("Field '$field' is required");
            }
        }

        // Validate title length
        if (strlen($data['title']) < 3) {
            throw new \InvalidArgumentException('Title must be at least 3 characters long');
        }

        if (strlen($data['title']) > 255) {
            throw new \InvalidArgumentException('Title cannot be longer than 255 characters');
        }

        // Validate content length
        if (strlen($data['content']) < 10) {
            throw new \InvalidArgumentException('Content must be at least 10 characters long');
        }

        // Validate article type
        try {
            ArticleType::from($data['article_type']);
        } catch (\ValueError $e) {
            throw new \InvalidArgumentException('Invalid article type');
        }
    }

    /**
     * Validate type-specific requirements
     */
    private function validateTypeRequirements(ArticleType $type, array $data): void
    {
        // Products must have price
        if ($type->requiresPrice()) {
            if (empty($data['price']) || (float)$data['price'] <= 0) {
                throw new \InvalidArgumentException(
                    sprintf('Article type %s requires a valid price', $type->getLabel())
                );
            }

            // Validate currency
            if (!empty($data['currency'])) {
                try {
                    Currency::from($data['currency']);
                } catch (\ValueError $e) {
                    throw new \InvalidArgumentException('Invalid currency');
                }
            }
        }

        // Digital products must have delivery type
        if ($type === ArticleType::PRODUCT_DIGITAL) {
            if (empty($data['digital_delivery_type'])) {
                throw new \InvalidArgumentException('Digital products must have a delivery type');
            }

            try {
                DigitalDeliveryType::from($data['digital_delivery_type']);
            } catch (\ValueError $e) {
                throw new \InvalidArgumentException('Invalid digital delivery type');
            }
        }

        // Physical products inventory validation
        if ($type === ArticleType::PRODUCT_PHYSICAL && isset($data['inventory'])) {
            if (!is_numeric($data['inventory']) || (int)$data['inventory'] < 0) {
                throw new \InvalidArgumentException('Inventory must be a non-negative number');
            }
        }

        // Validate SKU format if provided
        if (!empty($data['sku'])) {
            if (!preg_match('/^[A-Z0-9\-_]+$/i', $data['sku'])) {
                throw new \InvalidArgumentException('SKU can only contain letters, numbers, hyphens, and underscores');
            }

            if (strlen($data['sku']) > 50) {
                throw new \InvalidArgumentException('SKU cannot be longer than 50 characters');
            }
        }
    }

    /**
     * Generate unique slug from title
     */
    private function generateSlug(string $title): string
    {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower(trim($title));
        
        // Remove special characters and replace with hyphens
        $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug);
        $slug = preg_replace('/[\s\-]+/', '-', $slug);
        $slug = trim($slug, '-');

        // Ensure slug is not empty
        if (empty($slug)) {
            $slug = 'article-' . time();
        }

        // Ensure slug is unique
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->articleRepository->slugExists($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
            
            // Prevent infinite loop
            if ($counter > 1000) {
                $slug = $originalSlug . '-' . uniqid();
                break;
            }
        }

        return $slug;
    }

    /**
     * Get form data for article creation
     */
    public function getFormData(): array
    {
        return [
            'article_types' => $this->getArticleTypes(),
            'article_statuses' => $this->getArticleStatuses(),
            'currencies' => $this->getCurrencies(),
            'digital_delivery_types' => $this->getDigitalDeliveryTypes(),
        ];
    }

    /**
     * Get available article types
     */
    private function getArticleTypes(): array
    {
        $types = [];
        foreach (ArticleType::cases() as $type) {
            $types[] = [
                'value' => $type->value,
                'label' => $type->getLabel(),
                'description' => $type->getDescription(),
                'requires_price' => $type->requiresPrice(),
                'requires_sku' => $type->requiresSku(),
                'supports_inventory' => $type->supportsInventory(),
                'supports_digital_delivery' => $type->supportsDigitalDelivery(),
                'color' => $type->getColor(),
                'icon' => $type->getIcon(),
            ];
        }
        return $types;
    }

    /**
     * Get available article statuses
     */
    private function getArticleStatuses(): array
    {
        $statuses = [];
        foreach (ArticleStatus::cases() as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->getLabel(),
                'description' => $status->getDescription(),
                'is_public' => $status->isPublic(),
                'is_editable' => $status->isEditable(),
                'color' => $status->getColor(),
            ];
        }
        return $statuses;
    }

    /**
     * Get available currencies
     */
    private function getCurrencies(): array
    {
        $currencies = [];
        foreach (Currency::cases() as $currency) {
            $currencies[] = [
                'value' => $currency->value,
                'label' => $currency->value,
            ];
        }
        return $currencies;
    }

    /**
     * Get available digital delivery types
     */
    private function getDigitalDeliveryTypes(): array
    {
        $types = [];
        foreach (DigitalDeliveryType::cases() as $type) {
            $types[] = [
                'value' => $type->value,
                'label' => $type->getLabel(),
                'description' => $type->getDescription(),
                'requires_file' => $type->requiresFile(),
                'is_automated' => $type->isAutomated(),
            ];
        }
        return $types;
    }
}
