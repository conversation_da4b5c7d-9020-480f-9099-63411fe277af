<?php

declare(strict_types = 1);

namespace App\Module\Article\Read\Service;

use App\Module\Article\Domain\Repository\ArticleRepositoryInterface;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Enum\ArticleStatus;

final readonly class ArticleListService
{
    public function __construct(
        private ArticleRepositoryInterface $articleRepository,
    ) {
    }

    /**
     * Get article list for admin dashboard
     */
    public function getArticleList(int $limit = 20, int $offset = 0, ?string $status = null, ?string $type = null, ?string $search = null): array
    {
        $articles = [];

        if ($search) {
            // Use full-text search
            $articles = $this->articleRepository->search($search, $limit, $offset);
        } elseif ($status && $type) {
            // Filter by both status and type
            $statusEnum = ArticleStatus::from($status);
            $typeEnum = ArticleType::from($type);
            
            // For now, get by status and filter by type (could be optimized with custom query)
            $allByStatus = $this->articleRepository->findByStatus($statusEnum, $limit * 2, $offset);
            $articles = array_filter($allByStatus, fn($article) => $article->getType() === $typeEnum);
            $articles = array_slice($articles, 0, $limit);
        } elseif ($status) {
            $statusEnum = ArticleStatus::from($status);
            $articles = $this->articleRepository->findByStatus($statusEnum, $limit, $offset);
        } elseif ($type) {
            $typeEnum = ArticleType::from($type);
            $articles = $this->articleRepository->findByType($typeEnum, $limit, $offset);
        } else {
            $articles = $this->articleRepository->findLatest($limit, $offset);
        }

        // Convert articles to array format for admin display
        $articleData = [];
        foreach ($articles as $article) {
            $articleArray = $article->toArray();
            
            // Add computed fields for admin display
            $articleArray['word_count'] = str_word_count(strip_tags($articleArray['content']));
            $articleArray['reading_time'] = $this->calculateReadingTime($articleArray['content']);
            $articleArray['seo_score'] = $this->calculateSeoScore($articleArray);
            
            $articleData[] = $articleArray;
        }

        // Calculate pagination
        $totalArticles = $this->getTotalCount($status, $type, $search);
        $totalPages = ceil($totalArticles / $limit);
        $currentPage = floor($offset / $limit) + 1;

        return [
            'articles' => $articleData,
            'pagination' => [
                'total' => $totalArticles,
                'limit' => $limit,
                'offset' => $offset,
                'current_page' => $currentPage,
                'total_pages' => $totalPages,
                'has_next' => $currentPage < $totalPages,
                'has_prev' => $currentPage > 1,
            ],
            'filters' => [
                'status' => $status,
                'type' => $type,
                'search' => $search,
                'available_statuses' => $this->getAvailableStatuses(),
                'available_types' => $this->getAvailableTypes(),
            ]
        ];
    }

    /**
     * Get article by ID for admin
     */
    public function getArticleByStringId(string $articleId): array
    {
        $article = $this->articleRepository->findByStringId($articleId);
        
        if (!$article) {
            throw new \Exception('Article not found');
        }

        $articleArray = $article->toArray();
        
        // Add computed fields for admin display
        $articleArray['word_count'] = str_word_count(strip_tags($articleArray['content']));
        $articleArray['reading_time'] = $this->calculateReadingTime($articleArray['content']);
        $articleArray['seo_score'] = $this->calculateSeoScore($articleArray);
        
        return $articleArray;
    }

    /**
     * Get article statistics for dashboard
     */
    public function getArticleStatistics(): array
    {
        try {
            $stats = $this->articleRepository->getStatistics();

            // Calculate growth (mock data for now - would need historical data)
            $articlesGrowth = '+' . rand(1, 10) . ' tento týždeň';

            return [
                'totalArticles' => $stats['total'],
                'articlesGrowth' => $articlesGrowth,
                'publishedArticles' => $stats['published'],
                'draftArticles' => $stats['by_status']['draft'] ?? 0,
                'archivedArticles' => $stats['by_status']['archived'] ?? 0,
                'totalProducts' => $stats['products'],
                'publishedPercentage' => $stats['total'] > 0 ? round(($stats['published'] / $stats['total']) * 100) : 0,
                'by_status' => $stats['by_status'],
                'by_type' => $stats['by_type'],
            ];

        } catch (\Exception $e) {
            // Return default values if there's an error
            return [
                'totalArticles' => 0,
                'articlesGrowth' => '+0 tento týždeň',
                'publishedArticles' => 0,
                'draftArticles' => 0,
                'archivedArticles' => 0,
                'totalProducts' => 0,
                'publishedPercentage' => 0,
                'by_status' => [],
                'by_type' => [],
            ];
        }
    }

    /**
     * Get recent articles for dashboard
     */
    public function getRecentArticles(int $limit = 5): array
    {
        $articles = $this->articleRepository->findLatest($limit);
        
        $recentArticles = [];
        foreach ($articles as $article) {
            $articleArray = $article->toArray();
            
            $recentArticles[] = [
                'id' => $articleArray['id'],
                'title' => $articleArray['title'],
                'type' => $articleArray['type'],
                'type_label' => $articleArray['type_label'],
                'status' => $articleArray['status'],
                'status_label' => $articleArray['status_label'],
                'created_at' => $articleArray['created_at'],
                'author_id' => $articleArray['author_id'],
                'is_featured' => $articleArray['is_featured'],
                'is_product' => $articleArray['is_product'],
                'price' => $articleArray['price'],
            ];
        }
        
        return $recentArticles;
    }

    /**
     * Get featured articles
     */
    public function getFeaturedArticles(int $limit = 10): array
    {
        $articles = $this->articleRepository->findFeatured($limit);
        
        $featuredArticles = [];
        foreach ($articles as $article) {
            $featuredArticles[] = $article->toArray();
        }
        
        return $featuredArticles;
    }

    /**
     * Get products for shop
     */
    public function getProducts(int $limit = 20, int $offset = 0): array
    {
        $products = $this->articleRepository->findProducts($limit, $offset);
        
        $productData = [];
        foreach ($products as $product) {
            $productArray = $product->toArray();
            
            // Add product-specific computed fields
            $productArray['in_stock'] = $product->isInStock();
            $productArray['can_purchase'] = $product->canBePurchased();
            
            $productData[] = $productArray;
        }
        
        return $productData;
    }

    /**
     * Search articles
     */
    public function searchArticles(string $query, int $limit = 10, int $offset = 0): array
    {
        $articles = $this->articleRepository->search($query, $limit, $offset);
        
        $searchResults = [];
        foreach ($articles as $article) {
            $searchResults[] = $article->toArray();
        }
        
        return $searchResults;
    }

    /**
     * Calculate reading time in minutes
     */
    private function calculateReadingTime(string $content): int
    {
        $wordCount = str_word_count(strip_tags($content));
        $wordsPerMinute = 200; // Average reading speed
        
        return max(1, ceil($wordCount / $wordsPerMinute));
    }

    /**
     * Calculate basic SEO score
     */
    private function calculateSeoScore(array $article): int
    {
        $score = 0;
        
        // Title length (optimal: 30-60 characters)
        $titleLength = strlen($article['title']);
        if ($titleLength >= 30 && $titleLength <= 60) {
            $score += 20;
        } elseif ($titleLength >= 20 && $titleLength <= 80) {
            $score += 10;
        }
        
        // Meta description
        if (!empty($article['meta_description'])) {
            $metaLength = strlen($article['meta_description']);
            if ($metaLength >= 120 && $metaLength <= 160) {
                $score += 20;
            } elseif ($metaLength >= 100 && $metaLength <= 200) {
                $score += 10;
            }
        }
        
        // Content length (optimal: 300+ words)
        $wordCount = str_word_count(strip_tags($article['content']));
        if ($wordCount >= 300) {
            $score += 20;
        } elseif ($wordCount >= 150) {
            $score += 10;
        }
        
        // Has excerpt
        if (!empty($article['excerpt'])) {
            $score += 10;
        }
        
        // Has image
        if (!empty($article['image'])) {
            $score += 10;
        }
        
        // Meta keywords
        if (!empty($article['meta_keywords'])) {
            $score += 10;
        }
        
        // Slug quality (no spaces, reasonable length)
        if (!empty($article['slug']) && !str_contains($article['slug'], ' ') && strlen($article['slug']) <= 60) {
            $score += 10;
        }
        
        return min(100, $score);
    }

    /**
     * Get total count for pagination
     */
    private function getTotalCount(?string $status, ?string $type, ?string $search): int
    {
        if ($search) {
            // For search, we'd need a separate count method - for now return repository total
            return $this->articleRepository->count();
        }
        
        if ($status && $type) {
            // Would need custom query - for now use status count
            $statusEnum = ArticleStatus::from($status);
            return $this->articleRepository->countByStatus($statusEnum);
        }
        
        if ($status) {
            $statusEnum = ArticleStatus::from($status);
            return $this->articleRepository->countByStatus($statusEnum);
        }
        
        if ($type) {
            $typeEnum = ArticleType::from($type);
            return $this->articleRepository->countByType($typeEnum);
        }
        
        return $this->articleRepository->count();
    }

    /**
     * Get available statuses for filter
     */
    private function getAvailableStatuses(): array
    {
        $statuses = [];
        foreach (ArticleStatus::cases() as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->getLabel(),
                'count' => $this->articleRepository->countByStatus($status),
            ];
        }
        return $statuses;
    }

    /**
     * Get available types for filter
     */
    private function getAvailableTypes(): array
    {
        $types = [];
        foreach (ArticleType::cases() as $type) {
            $types[] = [
                'value' => $type->value,
                'label' => $type->getLabel(),
                'count' => $this->articleRepository->countByType($type),
                'color' => $type->getColor(),
                'icon' => $type->getIcon(),
            ];
        }
        return $types;
    }
}
