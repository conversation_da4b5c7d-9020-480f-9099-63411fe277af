<?php

declare(strict_types = 1);

namespace App\Module\Article\Read\Service;

use App\Module\Article\Domain\Repository\ArticleRepository;
use App\Module\Article\Infrastructure\Persistence\DbArticleRepository;
use App\Core\Domain\ValueObject\ArticleId;

final readonly class ArticleListService
{
    public function __construct(
        private DbArticleRepository $articleRepository,
    ) {
    }

    /**
     * Get article list for admin dashboard
     *
     * @param int $limit
     * @param int $offset
     * @param string|null $status
     * @param string|null $search
     * @return array
     */
    public function getArticleList(int $limit = 20, int $offset = 0, ?string $status = null, ?string $search = null): array
    {
        // For now, we'll use the existing findLatest method
        // In a real implementation, you'd want to add filtering and pagination to the repository
        $articles = $this->articleRepository->findLatest($limit);

        // Convert articles to array format for admin display
        $articleData = [];
        foreach ($articles as $article) {
            $articleArray = $article->toArray();

            // Add computed fields for admin display
            $articleArray['status_label'] = $this->getStatusLabel($articleArray['status']);
            $articleArray['type_label'] = $this->getTypeLabel($articleArray['article_type']);
            $articleArray['word_count'] = str_word_count(strip_tags($articleArray['content']));
            $articleArray['reading_time'] = $this->calculateReadingTime($articleArray['content']);

            $articleData[] = $articleArray;
        }

        // Mock pagination data - in real implementation, get from repository
        $totalArticles = $this->articleRepository->count();
        $totalPages = ceil($totalArticles / $limit);
        $currentPage = floor($offset / $limit) + 1;

        return [
            'articles' => $articleData,
            'pagination' => [
                'total' => $totalArticles,
                'limit' => $limit,
                'offset' => $offset,
                'current_page' => $currentPage,
                'total_pages' => $totalPages,
                'has_next' => $currentPage < $totalPages,
                'has_prev' => $currentPage > 1,
            ],
            'filters' => [
                'status' => $status,
                'search' => $search,
                'available_statuses' => ['draft', 'published', 'archived'],
                'available_types' => ['blog_post', 'review', 'tutorial'],
            ]
        ];
    }

    /**
     * Get article by ID for admin
     *
     * @param string $articleId
     * @return array
     * @throws \Exception
     */
    public function getArticleById(string $articleId): array
    {
        $article = $this->articleRepository->findByStringId($articleId);

        if (!$article) {
            throw new \Exception('Article not found');
        }

        $articleArray = $article->toArray();

        // Add computed fields for admin display
        $articleArray['status_label'] = $this->getStatusLabel($articleArray['status']);
        $articleArray['type_label'] = $this->getTypeLabel($articleArray['article_type']);
        $articleArray['word_count'] = str_word_count(strip_tags($articleArray['content']));
        $articleArray['reading_time'] = $this->calculateReadingTime($articleArray['content']);
        $articleArray['seo_score'] = $this->calculateSeoScore($articleArray);

        return $articleArray;
    }

    /**
     * Get article statistics for dashboard
     *
     * @return array
     */
    public function getArticleStatistics(): array
    {
        try {
            // Get total articles count
            $totalArticles = $this->articleRepository->count();

            // For now, we'll use mock data for different statuses
            // In real implementation, you'd add status filtering to repository
            $publishedArticles = round($totalArticles * 0.7); // Mock: 70% published
            $draftArticles = round($totalArticles * 0.25);    // Mock: 25% draft
            $archivedArticles = $totalArticles - $publishedArticles - $draftArticles;

            // Calculate growth (mock data for now - would need historical data)
            $articlesGrowth = '+5 tento týždeň';

            return [
                'totalArticles' => $totalArticles,
                'articlesGrowth' => $articlesGrowth,
                'publishedArticles' => $publishedArticles,
                'draftArticles' => $draftArticles,
                'archivedArticles' => $archivedArticles,
                'publishedPercentage' => $totalArticles > 0 ? round(($publishedArticles / $totalArticles) * 100) : 0,
            ];

        } catch (\Exception $e) {
            // Return default values if there's an error
            return [
                'totalArticles' => 0,
                'articlesGrowth' => '+0 tento týždeň',
                'publishedArticles' => 0,
                'draftArticles' => 0,
                'archivedArticles' => 0,
                'publishedPercentage' => 0,
            ];
        }
    }

    /**
     * Get recent articles for dashboard
     *
     * @param int $limit
     * @return array
     */
    public function getRecentArticles(int $limit = 5): array
    {
        $articles = $this->articleRepository->findLatest($limit);

        $recentArticles = [];
        foreach ($articles as $article) {
            $articleArray = $article->toArray();

            $recentArticles[] = [
                'id' => $articleArray['id'],
                'title' => $articleArray['title'],
                'status' => $articleArray['status'],
                'status_label' => $this->getStatusLabel($articleArray['status']),
                'created_at' => $articleArray['created_at'],
                'author_id' => $articleArray['author_id'],
                'is_featured' => $articleArray['is_featured'],
            ];
        }

        return $recentArticles;
    }

    /**
     * Get status label for display
     */
    private function getStatusLabel(string $status): string
    {
        return match($status) {
            'draft' => 'Koncept',
            'published' => 'Publikovaný',
            'archived' => 'Archivovaný',
            default => ucfirst($status),
        };
    }

    /**
     * Get type label for display
     */
    private function getTypeLabel(?string $type): string
    {
        if (!$type) {
            return 'Neurčený';
        }

        return match($type) {
            'blog_post' => 'Blog príspevok',
            'review' => 'Recenzia',
            'tutorial' => 'Tutoriál',
            default => ucfirst($type),
        };
    }

    /**
     * Calculate reading time in minutes
     */
    private function calculateReadingTime(string $content): int
    {
        $wordCount = str_word_count(strip_tags($content));
        $wordsPerMinute = 200; // Average reading speed

        return max(1, ceil($wordCount / $wordsPerMinute));
    }

    /**
     * Calculate basic SEO score
     */
    private function calculateSeoScore(array $article): int
    {
        $score = 0;

        // Title length (optimal: 30-60 characters)
        $titleLength = strlen($article['title']);
        if ($titleLength >= 30 && $titleLength <= 60) {
            $score += 20;
        } elseif ($titleLength >= 20 && $titleLength <= 80) {
            $score += 10;
        }

        // Meta description
        if (!empty($article['meta_description'])) {
            $metaLength = strlen($article['meta_description']);
            if ($metaLength >= 120 && $metaLength <= 160) {
                $score += 20;
            } elseif ($metaLength >= 100 && $metaLength <= 200) {
                $score += 10;
            }
        }

        // Content length (optimal: 300+ words)
        $wordCount = str_word_count(strip_tags($article['content']));
        if ($wordCount >= 300) {
            $score += 20;
        } elseif ($wordCount >= 150) {
            $score += 10;
        }

        // Has excerpt
        if (!empty($article['excerpt'])) {
            $score += 10;
        }

        // Has image
        if (!empty($article['image'])) {
            $score += 10;
        }

        // Meta keywords
        if (!empty($article['meta_keywords'])) {
            $score += 10;
        }

        // Slug quality (no spaces, reasonable length)
        if (!empty($article['slug']) && !str_contains($article['slug'], ' ') && strlen($article['slug']) <= 60) {
            $score += 10;
        }

        return min(100, $score);
    }
}
