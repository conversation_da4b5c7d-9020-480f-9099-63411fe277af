<?php

declare(strict_types = 1);

namespace App\Module\Article\Infrastructure\Persistence;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Repository\ArticleRepositoryInterface;
use App\Module\Article\Domain\ValueObject\ArticleId;
use App\Module\Article\Domain\ValueObject\Price;
use App\Module\Article\Domain\ValueObject\SKU;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Enum\ArticleStatus;
use App\Module\Article\Domain\Enum\DigitalDeliveryType;
use App\Core\Domain\ValueObject\EntityId;
use App\Core\Domain\Enum\Currency;
use Cake\Database\Connection as CakeConnection;

final class DbArticleRepository implements ArticleRepositoryInterface
{
    private CakeConnection $connection;

    public function __construct(CakeConnection $connection)
    {
        $this->connection = $connection;
    }

    public function findById(EntityId $id): ?Article
    {
        if (!$id->isForEntity('Article')) {
            return null;
        }
        return $this->find(ArticleId::fromString($id->toRfc4122()));
    }

    public function find(ArticleId $id): ?Article
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE id = :id AND deleted_at IS NULL',
            ['id' => $id->toRfc4122()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findByStringId(string $id): ?Article
    {
        return $this->find(ArticleId::fromString($id));
    }

    public function findBySlug(string $slug): ?Article
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE slug = :slug AND deleted_at IS NULL',
            ['slug' => $slug]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findBySku(SKU $sku): ?Article
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE sku = :sku AND deleted_at IS NULL',
            ['sku' => $sku->getValue()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findByType(ArticleType $type, int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE type = :type AND deleted_at IS NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'type' => $type->value,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findByStatus(ArticleStatus $status, int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE status = :status AND deleted_at IS NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'status' => $status->value,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findPublished(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE status = :status AND is_public = 1 AND deleted_at IS NULL ORDER BY published_at DESC, created_at DESC LIMIT :limit OFFSET :offset',
            [
                'status' => ArticleStatus::PUBLISHED->value,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findFeatured(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE is_featured = 1 AND status = :status AND is_public = 1 AND deleted_at IS NULL ORDER BY published_at DESC, created_at DESC LIMIT :limit OFFSET :offset',
            [
                'status' => ArticleStatus::PUBLISHED->value,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findLatest(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findProducts(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE type IN (:physical, :digital) AND deleted_at IS NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'physical' => ArticleType::PRODUCT_PHYSICAL->value,
                'digital' => ArticleType::PRODUCT_DIGITAL->value,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function findByAuthor(string $authorId, int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE author_id = :author_id AND deleted_at IS NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset',
            [
                'author_id' => $authorId,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function search(string $query, int $limit = 10, int $offset = 0): array
    {
        // Use FTS for full-text search
        $statement = $this->connection->execute(
            'SELECT a.* FROM articles a
             JOIN articles_fts fts ON a.id = fts.id
             WHERE articles_fts MATCH :query AND a.deleted_at IS NULL
             ORDER BY a.created_at DESC LIMIT :limit OFFSET :offset',
            [
                'query' => $query,
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        return $this->mapResults($statement->fetchAll('assoc'));
    }

    public function countByType(ArticleType $type): int
    {
        $statement = $this->connection->execute(
            'SELECT COUNT(*) as count FROM articles WHERE type = :type AND deleted_at IS NULL',
            ['type' => $type->value]
        );

        $result = $statement->fetch('assoc');
        return (int)$result['count'];
    }

    public function countByStatus(ArticleStatus $status): int
    {
        $statement = $this->connection->execute(
            'SELECT COUNT(*) as count FROM articles WHERE status = :status AND deleted_at IS NULL',
            ['status' => $status->value]
        );

        $result = $statement->fetch('assoc');
        return (int)$result['count'];
    }

    public function countPublished(): int
    {
        return $this->countByStatus(ArticleStatus::PUBLISHED);
    }

    public function countProducts(): int
    {
        $statement = $this->connection->execute(
            'SELECT COUNT(*) as count FROM articles WHERE type IN (:physical, :digital) AND deleted_at IS NULL',
            [
                'physical' => ArticleType::PRODUCT_PHYSICAL->value,
                'digital' => ArticleType::PRODUCT_DIGITAL->value,
            ]
        );

        $result = $statement->fetch('assoc');
        return (int)$result['count'];
    }

    public function slugExists(string $slug, ?ArticleId $excludeId = null): bool
    {
        $sql = 'SELECT 1 FROM articles WHERE slug = :slug AND deleted_at IS NULL';
        $params = ['slug' => $slug];

        if ($excludeId !== null) {
            $sql .= ' AND id != :exclude_id';
            $params['exclude_id'] = $excludeId->toRfc4122();
        }

        $statement = $this->connection->execute($sql, $params);
        return $statement->fetch() !== false;
    }

    public function skuExists(SKU $sku, ?ArticleId $excludeId = null): bool
    {
        $sql = 'SELECT 1 FROM articles WHERE sku = :sku AND deleted_at IS NULL';
        $params = ['sku' => $sku->getValue()];

        if ($excludeId !== null) {
            $sql .= ' AND id != :exclude_id';
            $params['exclude_id'] = $excludeId->toRfc4122();
        }

        $statement = $this->connection->execute($sql, $params);
        return $statement->fetch() !== false;
    }

    public function getStatistics(): array
    {
        // Get counts by status
        $statusStats = [];
        foreach (ArticleStatus::cases() as $status) {
            $statusStats[$status->value] = $this->countByStatus($status);
        }

        // Get counts by type
        $typeStats = [];
        foreach (ArticleType::cases() as $type) {
            $typeStats[$type->value] = $this->countByType($type);
        }

        return [
            'total' => $this->count(),
            'published' => $this->countPublished(),
            'products' => $this->countProducts(),
            'by_status' => $statusStats,
            'by_type' => $typeStats,
        ];
    }

    public function save(object $entity): void
    {
        if (!$entity instanceof Article) {
            throw new \InvalidArgumentException('Entity must be instance of Article');
        }

        $data = $this->mapToRow($entity);
        $existing = $this->find($entity->getId());

        if ($existing === null) {
            // Insert new article
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $this->connection->execute(
                "INSERT INTO articles ($columns) VALUES ($placeholders)",
                $data
            );
        } else {
            // Update existing article
            unset($data['id']);
            unset($data['created_at']);
            $data['updated_at'] = (new \DateTimeImmutable())->format('Y-m-d H:i:s');

            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "$key = :$key";
            }
            $setClause = implode(', ', $setClause);

            $this->connection->execute(
                "UPDATE articles SET $setClause WHERE id = :id",
                array_merge($data, ['id' => $entity->getId()->toRfc4122()])
            );
        }
    }

    public function delete(object $entity): void
    {
        if (!$entity instanceof Article) {
            throw new \InvalidArgumentException('Entity must be instance of Article');
        }

        // Soft delete
        $this->connection->execute(
            'UPDATE articles SET deleted_at = :deleted_at WHERE id = :id',
            [
                'deleted_at' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
                'id' => $entity->getId()->toRfc4122(),
            ]
        );
    }

    public function count(): int
    {
        $statement = $this->connection->execute('SELECT COUNT(*) as count FROM articles WHERE deleted_at IS NULL');
        $result = $statement->fetch('assoc');
        return (int)$result['count'];
    }

    public function findAll(int $limit = 10, int $offset = 0): array
    {
        return $this->findLatest($limit, $offset);
    }

    public function exists(EntityId $id): bool
    {
        if (!$id->isForEntity('Article')) {
            return false;
        }

        $statement = $this->connection->execute(
            'SELECT 1 FROM articles WHERE id = :id AND deleted_at IS NULL LIMIT 1',
            ['id' => $id->toRfc4122()]
        );

        return $statement->fetch() !== false;
    }

    /**
     * Map database row to Article entity
     */
    private function mapToEntity(array $row): Article
    {
        // Handle price
        $price = null;
        if ($row['price_amount'] !== null) {
            $price = Price::create(
                (float)$row['price_amount'],
                Currency::from($row['price_currency'])
            );
        }

        // Handle SKU
        $sku = $row['sku'] ? SKU::fromString($row['sku']) : null;

        // Handle digital delivery type
        $digitalDeliveryType = $row['digital_delivery_type']
            ? DigitalDeliveryType::from($row['digital_delivery_type'])
            : null;

        return new Article(
            ArticleId::fromString($row['id']),
            $row['title'],
            $row['slug'],
            ArticleType::from($row['type']),
            ArticleStatus::from($row['status']),
            $row['content'],
            $row['excerpt'],
            $row['image'],
            $row['author_id'],
            $price,
            $sku,
            $row['inventory'] !== null ? (int)$row['inventory'] : null,
            $digitalDeliveryType,
            $row['digital_file'],
            $row['meta_title'],
            $row['meta_description'],
            $row['meta_keywords'],
            (bool)$row['is_featured'],
            (bool)$row['is_public'],
            $row['published_at'] ? new \DateTimeImmutable($row['published_at']) : null,
            $row['scheduled_at'] ? new \DateTimeImmutable($row['scheduled_at']) : null,
            new \DateTimeImmutable($row['created_at']),
            $row['updated_at'] ? new \DateTimeImmutable($row['updated_at']) : null,
            $row['deleted_at'] ? new \DateTimeImmutable($row['deleted_at']) : null,
        );
    }

    /**
     * Map Article entity to database row
     */
    private function mapToRow(Article $article): array
    {
        return [
            'id' => $article->getId()->toRfc4122(),
            'title' => $article->getTitle(),
            'slug' => $article->getSlug(),
            'type' => $article->getType()->value,
            'status' => $article->getStatus()->value,
            'content' => $article->getContent(),
            'excerpt' => $article->getExcerpt(),
            'image' => $article->getImage(),
            'author_id' => $article->getAuthorId(),
            'price_amount' => $article->getPrice()?->getAmount(),
            'price_currency' => $article->getPrice()?->getCurrency()->value,
            'sku' => $article->getSku()?->getValue(),
            'inventory' => $article->getInventory(),
            'digital_delivery_type' => $article->getDigitalDeliveryType()?->value,
            'digital_file' => $article->getDigitalFile(),
            'meta_title' => $article->getMetaTitle(),
            'meta_description' => $article->getMetaDescription(),
            'meta_keywords' => $article->getMetaKeywords(),
            'is_featured' => $article->isFeatured() ? 1 : 0,
            'is_public' => $article->isPublic() ? 1 : 0,
            'published_at' => $article->getPublishedAt()?->format('Y-m-d H:i:s'),
            'scheduled_at' => $article->getScheduledAt()?->format('Y-m-d H:i:s'),
            'created_at' => $article->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $article->getUpdatedAt()?->format('Y-m-d H:i:s'),
            'deleted_at' => $article->getDeletedAt()?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Map multiple results to entities
     */
    private function mapResults(array $results): array
    {
        $articles = [];
        foreach ($results as $result) {
            $articles[] = $this->mapToEntity($result);
        }
        return $articles;
    }
}
