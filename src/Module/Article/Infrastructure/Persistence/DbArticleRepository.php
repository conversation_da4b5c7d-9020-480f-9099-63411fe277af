<?php

declare(strict_types = 1);

namespace App\Module\Article\Infrastructure\Persistence;

use App\Infrastructure\Persistence\BaseRepository;
use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Repository\ArticleRepository;
use App\Core\Domain\ValueObject\ArticleId;
use App\Core\Domain\Enum\Currency;
use App\Core\Domain\Enum\ProductStatus;
use App\Core\Domain\Enum\ProductType;
use Cake\Database\Connection as CakeConnection;

final class DbArticleRepository extends BaseRepository implements ArticleRepository
{
    public function __construct(
        CakeConnection $connection,
    ) {
        parent::__construct($connection);
    }

    public function find(ArticleId $id): ?Article
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE id = :id',
            ['id' => $id->toRfc4122()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findById(string $id): ?Article
    {
        return $this->find(ArticleId::fromString($id));
    }

    public function findBySlug(string $slug): ?Article
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE slug = :slug',
            ['slug' => $slug]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function findLatest(int $limit = 10): array
    {
        $statement = $this->connection->execute(
            'SELECT * FROM articles WHERE status = :status AND is_public = 1 ORDER BY published_at DESC, created_at DESC LIMIT :limit',
            [
                'status' => ProductStatus::PUBLISHED->value,
                'limit' => $limit,
            ]
        );

        $results = $statement->fetchAll('assoc');

        $articles = [];
        foreach ($results as $result) {
            $articles[] = $this->mapToEntity($result);
        }

        return $articles;
    }

    public function save(object $entity): void
    {
        if (!$entity instanceof Article) {
            throw new \InvalidArgumentException('Entity must be instance of Article');
        }
        $this->saveArticle($entity);
    }

    public function saveArticle(Article $article): void
    {
        $data = [
            'id' => $article->getId()->toRfc4122(),
            'title' => $article->getTitle(),
            'slug' => $article->getSlug(),
            'excerpt' => $article->getExcerpt(),
            'content' => $article->getContent(),
            'image' => $article->getImage(),
            'author_id' => $article->getAuthorId(),
            'product_type' => $article->getProductType()->value,
            'article_type' => $article->getArticleType()?->value,
            'product_subtype' => $article->getProductSubtype(),
            'sku' => $article->getSku(),
            'price' => $article->getPrice(),
            'currency' => $article->getCurrency()->value,
            'status' => $article->getStatus()->value,
            'is_featured' => $article->isFeatured() ? 1 : 0,
            'is_public' => $article->isPublic() ? 1 : 0,
            'meta_title' => $article->getMetaTitle(),
            'meta_description' => $article->getMetaDescription(),
            'meta_keywords' => $article->getMetaKeywords(),
            'published_at' => $article->getPublishedAt()?->format('Y-m-d H:i:s'),
            'created_at' => $article->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $article->getUpdatedAt()?->format('Y-m-d H:i:s'),
            'deleted_at' => $article->getDeletedAt()?->format('Y-m-d H:i:s'),
        ];

        $existing = $this->find($article->getId());

        if ($existing === null) {
            // Insert new article
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $this->connection->execute(
                "INSERT INTO articles ($columns) VALUES ($placeholders)",
                $data
            );
        } else {
            // Update existing article
            unset($data['id']);
            unset($data['created_at']);

            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "$key = :$key";
            }
            $setClause = implode(', ', $setClause);

            $this->connection->execute(
                "UPDATE articles SET $setClause WHERE id = :id",
                array_merge($data, ['id' => $article->getId()->toRfc4122()])
            );
        }
    }

    public function delete(object $entity): void
    {
        if (!$entity instanceof Article) {
            throw new \InvalidArgumentException('Entity must be instance of Article');
        }
        $this->deleteArticle($entity);
    }

    public function deleteArticle(Article $article): void
    {
        $this->connection->execute(
            'DELETE FROM articles WHERE id = :id',
            ['id' => $article->getId()->toRfc4122()]
        );
    }

    public function count(): int
    {
        $statement = $this->connection->execute('SELECT COUNT(*) as count FROM articles');
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    /**
     * Map database row to Article entity
     */
    private function mapToEntity(array $row): Article
    {
        return new Article(
            ArticleId::fromString($row['id']),
            $row['title'],
            $row['slug'],
            $row['excerpt'],
            $row['content'],
            $row['image'],
            $row['author_id'],
            ProductType::from($row['product_type']),
            $row['article_type'] ? ArticleType::from($row['article_type']) : null,
            $row['product_subtype'],
            $row['sku'],
            $row['price'] ? (float)$row['price'] : null,
            Currency::from($row['currency']),
            ProductStatus::from($row['status']),
            (bool)$row['is_featured'],
            (bool)$row['is_public'],
            $row['meta_title'],
            $row['meta_description'],
            $row['meta_keywords'],
            $row['published_at'] ? new \DateTimeImmutable($row['published_at']) : null,
            new \DateTimeImmutable($row['created_at']),
            $row['updated_at'] ? new \DateTimeImmutable($row['updated_at']) : null,
            $row['deleted_at'] ? new \DateTimeImmutable($row['deleted_at']) : null,
        );
    }
}
