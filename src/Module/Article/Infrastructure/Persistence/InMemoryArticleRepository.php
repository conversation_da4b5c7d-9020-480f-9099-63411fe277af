<?php

declare(strict_types = 1);

namespace App\Module\Article\Infrastructure\Persistence;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\ValueObject\ArticleId;
use App\Shared\Domain\Enum\Currency;
use App\Shared\Domain\Enum\ProductStatus;
use App\Shared\Domain\Enum\ProductType;
use App\Module\Article\Domain\Repository\ArticleRepository;

final class InMemoryArticleRepository implements ArticleRepository
{
    /** @var array<string, Article> */
    private array $articles = [];

    public function __construct()
    {
        $this->initializeSampleData();
    }

    public function findLatest(int $limit = 10): array
    {
        return array_slice(array_values($this->articles), 0, $limit);
    }

    public function find(ArticleId $id): ?Article
    {
        return $this->articles[$id->toRfc4122()] ?? null;
    }

    public function findById(string $id): ?Article
    {
        return $this->articles[$id] ?? null;
    }

    public function findBySlug(string $slug): ?Article
    {
        foreach ($this->articles as $article) {
            if ($article->getSlug() === $slug) {
                return $article;
            }
        }

        return null;
    }

    public function save(Article $article): void
    {
        $this->articles[$article->getId()->toRfc4122()] = $article;
    }

    public function delete(Article $article): void
    {
        unset($this->articles[$article->getId()->toRfc4122()]);
    }

    public function count(): int
    {
        return count($this->articles);
    }

    private function initializeSampleData(): void
    {
        $sampleData = [
            [
                'id' => '1',
                'title' => 'Gitarové stupnice pre začiatočníkov',
                'slug' => 'gitarove-stupnice-pre-zaciatocnikov',
                'excerpt' => 'Naučte sa základné gitarové stupnice a ako ich používať.',
                'content' => 'Kompletný obsah článku o gitarových stupniciach...',
                'image' => '/assets/themes/modern/images/web-dev.jpg',
                'article_type' => ArticleType::TUTORIAL,
                'sku' => 'ART-TUTOR-001',
                'price' => null, // Free article
                'date' => '2025-05-26',
            ],
            [
                'id' => '2',
                'title' => 'Pokročilé techniky hry na gitare',
                'slug' => 'pokrocile-techniky-hry-na-gitare',
                'excerpt' => 'Rozšírte svoje gitarové zručnosti s pokročilými technikami.',
                'content' => 'Detailný obsah o pokročilých technikách...',
                'image' => '/assets/themes/modern/images/javascript.jpg',
                'article_type' => ArticleType::TUTORIAL,
                'sku' => 'ART-TUTOR-002',
                'price' => 9.99, // Paid tutorial
                'date' => '2025-05-20',
            ],
            [
                'id' => '3',
                'title' => 'Základné akordy',
                'slug' => 'zakladne-akordy',
                'excerpt' => 'Naučte sa základné gitarové akordy pre začiatočníkov.',
                'content' => 'Základné informácie o akordoch...',
                'image' => '/assets/themes/modern/images/php82.jpg',
                'article_type' => ArticleType::BLOG_POST,
                'sku' => null, // No SKU for free blog post
                'price' => null,
                'date' => '2025-05-10',
            ],
        ];

        foreach ($sampleData as $data) {
            $article = new Article(
                ArticleId::fromString($data['id']),
                $data['title'],
                $data['slug'],
                $data['excerpt'],
                $data['content'],
                $data['image'],
                null, // author_id
                ProductType::ARTICLE,
                $data['article_type'],
                null, // product_subtype
                $data['sku'],
                $data['price'],
                Currency::EUR,
                ProductStatus::PUBLISHED,
                false, // is_featured
                true, // is_public
                null, // meta_title
                null, // meta_description
                null, // meta_keywords
                new \DateTimeImmutable($data['date']), // published_at
                new \DateTimeImmutable($data['date']), // created_at
                null, // updated_at
                null  // deleted_at
            );
            $this->articles[$data['id']] = $article;
        }
    }
}
