<?php

declare(strict_types = 1);

namespace App\Module\Article\Update\Service;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Repository\ArticleRepositoryInterface;
use App\Module\Article\Domain\ValueObject\ArticleId;
use App\Module\Article\Domain\ValueObject\Price;
use App\Module\Article\Domain\ValueObject\SKU;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Enum\ArticleStatus;
use App\Module\Article\Domain\Enum\DigitalDeliveryType;
use App\Core\Domain\Enum\Currency;

final readonly class ArticleUpdater
{
    public function __construct(
        private ArticleRepositoryInterface $articleRepository,
    ) {
    }

    /**
     * Update existing article
     */
    public function updateArticle(string $articleId, array $data): bool
    {
        // Find existing article
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        // Validate required fields
        $this->validateRequiredFields($data);

        // Parse enums
        $type = ArticleType::from($data['article_type']);
        $status = ArticleStatus::from($data['status'] ?? $article->getStatus()->value);

        // Validate type-specific requirements
        $this->validateTypeRequirements($type, $data);

        // Handle slug update
        $slug = $article->getSlug();
        if (!empty($data['slug']) && $data['slug'] !== $slug) {
            $newSlug = $this->sanitizeSlug($data['slug']);
            if ($this->articleRepository->slugExists($newSlug, $article->getId())) {
                throw new \InvalidArgumentException('Slug already exists');
            }
            $slug = $newSlug;
        }

        // Create price if provided
        $price = null;
        if ($type->requiresPrice() && isset($data['price']) && $data['price'] > 0) {
            $currency = Currency::from($data['currency'] ?? 'EUR');
            $price = Price::create((float)$data['price'], $currency);
        }

        // Handle SKU
        $sku = $article->getSku();
        if ($type->requiresSku()) {
            if (!empty($data['sku'])) {
                $newSku = SKU::fromString($data['sku']);
                
                // Check SKU uniqueness (excluding current article)
                if ($this->articleRepository->skuExists($newSku, $article->getId())) {
                    throw new \InvalidArgumentException('SKU already exists');
                }
                $sku = $newSku;
            } elseif (!$sku) {
                // Generate SKU if article doesn't have one
                $sku = SKU::generate($type->value, $data['title']);
                
                // Ensure generated SKU is unique
                $attempts = 0;
                while ($this->articleRepository->skuExists($sku, $article->getId()) && $attempts < 10) {
                    $sku = SKU::generate($type->value, $data['title']);
                    $attempts++;
                }
            }
        }

        // Handle digital delivery type
        $digitalDeliveryType = $article->getDigitalDeliveryType();
        if ($type === ArticleType::PRODUCT_DIGITAL && !empty($data['digital_delivery_type'])) {
            $digitalDeliveryType = DigitalDeliveryType::from($data['digital_delivery_type']);
        }

        // Handle inventory for physical products
        $inventory = $article->getInventory();
        if ($type === ArticleType::PRODUCT_PHYSICAL && isset($data['inventory'])) {
            $inventory = max(0, (int)$data['inventory']);
        }

        // Handle publishing
        $publishedAt = $article->getPublishedAt();
        $isPublic = (bool)($data['is_public'] ?? $article->isPublic());
        
        if ($status === ArticleStatus::PUBLISHED && !$publishedAt) {
            $publishedAt = new \DateTimeImmutable();
        }

        // Create updated article entity
        $updatedArticle = new Article(
            $article->getId(),
            trim($data['title']),
            $slug,
            $type,
            $status,
            trim($data['content']),
            !empty($data['excerpt']) ? trim($data['excerpt']) : null,
            !empty($data['image']) ? trim($data['image']) : null,
            !empty($data['author_id']) ? trim($data['author_id']) : $article->getAuthorId(),
            $price,
            $sku,
            $inventory,
            $digitalDeliveryType,
            !empty($data['digital_file']) ? trim($data['digital_file']) : $article->getDigitalFile(),
            !empty($data['meta_title']) ? trim($data['meta_title']) : null,
            !empty($data['meta_description']) ? trim($data['meta_description']) : null,
            !empty($data['meta_keywords']) ? trim($data['meta_keywords']) : null,
            (bool)($data['is_featured'] ?? $article->isFeatured()),
            $isPublic,
            $publishedAt,
            $article->getScheduledAt(),
            $article->getCreatedAt(),
            new \DateTimeImmutable(), // updated_at
        );

        // Save updated article
        $this->articleRepository->save($updatedArticle);

        return true;
    }

    /**
     * Update article status only
     */
    public function updateStatus(string $articleId, string $newStatus): bool
    {
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        $status = ArticleStatus::from($newStatus);
        $updatedArticle = $article->changeStatus($status);

        $this->articleRepository->save($updatedArticle);

        return true;
    }

    /**
     * Update article price
     */
    public function updatePrice(string $articleId, float $amount, string $currency): bool
    {
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        if (!$article->getType()->requiresPrice()) {
            throw new \InvalidArgumentException('This article type does not support pricing');
        }

        $price = Price::create($amount, Currency::from($currency));
        $updatedArticle = $article->updatePrice($price);

        $this->articleRepository->save($updatedArticle);

        return true;
    }

    /**
     * Update inventory for physical products
     */
    public function updateInventory(string $articleId, int $newInventory): bool
    {
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        $updatedArticle = $article->updateInventory($newInventory);

        $this->articleRepository->save($updatedArticle);

        return true;
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(string $articleId): bool
    {
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        $updatedArticle = $article->isFeatured() 
            ? $article->unmarkAsFeatured() 
            : $article->markAsFeatured();

        $this->articleRepository->save($updatedArticle);

        return true;
    }

    /**
     * Validate required fields
     */
    private function validateRequiredFields(array $data): void
    {
        $required = ['title', 'content', 'article_type'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \InvalidArgumentException("Field '$field' is required");
            }
        }

        // Validate title length
        if (strlen($data['title']) < 3) {
            throw new \InvalidArgumentException('Title must be at least 3 characters long');
        }

        if (strlen($data['title']) > 255) {
            throw new \InvalidArgumentException('Title cannot be longer than 255 characters');
        }

        // Validate content length
        if (strlen($data['content']) < 10) {
            throw new \InvalidArgumentException('Content must be at least 10 characters long');
        }

        // Validate article type
        try {
            ArticleType::from($data['article_type']);
        } catch (\ValueError $e) {
            throw new \InvalidArgumentException('Invalid article type');
        }
    }

    /**
     * Validate type-specific requirements
     */
    private function validateTypeRequirements(ArticleType $type, array $data): void
    {
        // Products must have price
        if ($type->requiresPrice()) {
            if (empty($data['price']) || (float)$data['price'] <= 0) {
                throw new \InvalidArgumentException(
                    sprintf('Article type %s requires a valid price', $type->getLabel())
                );
            }

            // Validate currency
            if (!empty($data['currency'])) {
                try {
                    Currency::from($data['currency']);
                } catch (\ValueError $e) {
                    throw new \InvalidArgumentException('Invalid currency');
                }
            }
        }

        // Digital products must have delivery type
        if ($type === ArticleType::PRODUCT_DIGITAL) {
            if (empty($data['digital_delivery_type'])) {
                throw new \InvalidArgumentException('Digital products must have a delivery type');
            }

            try {
                DigitalDeliveryType::from($data['digital_delivery_type']);
            } catch (\ValueError $e) {
                throw new \InvalidArgumentException('Invalid digital delivery type');
            }
        }

        // Physical products inventory validation
        if ($type === ArticleType::PRODUCT_PHYSICAL && isset($data['inventory'])) {
            if (!is_numeric($data['inventory']) || (int)$data['inventory'] < 0) {
                throw new \InvalidArgumentException('Inventory must be a non-negative number');
            }
        }

        // Validate SKU format if provided
        if (!empty($data['sku'])) {
            if (!preg_match('/^[A-Z0-9\-_]+$/i', $data['sku'])) {
                throw new \InvalidArgumentException('SKU can only contain letters, numbers, hyphens, and underscores');
            }

            if (strlen($data['sku']) > 50) {
                throw new \InvalidArgumentException('SKU cannot be longer than 50 characters');
            }
        }
    }

    /**
     * Sanitize slug
     */
    private function sanitizeSlug(string $slug): string
    {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower(trim($slug));
        
        // Remove special characters and replace with hyphens
        $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug);
        $slug = preg_replace('/[\s\-]+/', '-', $slug);
        $slug = trim($slug, '-');

        // Ensure slug is not empty
        if (empty($slug)) {
            throw new \InvalidArgumentException('Slug cannot be empty');
        }

        return $slug;
    }
}
