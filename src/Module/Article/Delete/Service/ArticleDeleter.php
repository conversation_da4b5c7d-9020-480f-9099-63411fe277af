<?php

declare(strict_types = 1);

namespace App\Module\Article\Delete\Service;

use App\Module\Article\Domain\Repository\ArticleRepositoryInterface;

final readonly class ArticleDeleter
{
    public function __construct(
        private ArticleRepositoryInterface $articleRepository,
    ) {
    }

    /**
     * Delete article (soft delete)
     */
    public function deleteArticle(string $articleId): bool
    {
        $article = $this->articleRepository->findByStringId($articleId);
        if (!$article) {
            throw new \Exception('Article not found');
        }

        // Check if article can be deleted
        if (!$this->canDelete($article)) {
            throw new \InvalidArgumentException('Article cannot be deleted');
        }

        // Perform soft delete
        $this->articleRepository->delete($article);

        return true;
    }

    /**
     * Bulk delete articles
     */
    public function bulkDelete(array $articleIds): array
    {
        $results = [];
        $errors = [];

        foreach ($articleIds as $articleId) {
            try {
                $this->deleteArticle($articleId);
                $results[] = $articleId;
            } catch (\Exception $e) {
                $errors[$articleId] = $e->getMessage();
            }
        }

        return [
            'deleted' => $results,
            'errors' => $errors,
            'total_deleted' => count($results),
            'total_errors' => count($errors),
        ];
    }

    /**
     * Check if article can be deleted
     */
    private function canDelete($article): bool
    {
        // Add business rules for deletion
        // For example:
        // - Published products with recent orders cannot be deleted
        // - Featured articles might need special permission
        // - etc.

        // For now, allow all deletions (soft delete is safe)
        return true;
    }
}
