<?php

declare(strict_types=1);

namespace App\Application\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * Security Headers Middleware
 *
 * Adds security headers to all responses to protect against common attacks
 */
class SecurityHeadersMiddleware implements MiddlewareInterface
{
    private array $headers;
    private bool $isHttps;

    public function __construct(array $securitySettings = [])
    {
        $this->isHttps = $securitySettings['force_https'] ?? false;

        // Default security headers
        $this->headers = [
            // Prevent MIME type sniffing
            'X-Content-Type-Options' => 'nosniff',

            // Enable XSS protection
            'X-XSS-Protection' => '1; mode=block',

            // Prevent clickjacking attacks
            'X-Frame-Options' => 'SAMEORIGIN',

            // Referrer Policy - control referrer information
            'Referrer-Policy' => 'strict-origin-when-cross-origin',

            // Content Security Policy (CSP) - adjust as needed for your app
            'Content-Security-Policy' => implode('; ', [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tiny.cloud", // Allow inline scripts and TinyMCE CDN
                "style-src 'self' 'unsafe-inline' https://cdn.tiny.cloud", // Allow inline styles and TinyMCE CDN
                "img-src 'self' data: https:", // Allow images from self, data URLs, and HTTPS
                "font-src 'self' data: https://cdn.tiny.cloud", // Allow fonts from self, data URLs, and TinyMCE CDN
                "connect-src 'self' https://cdn.tiny.cloud", // Allow AJAX requests to same origin and TinyMCE CDN
                "frame-ancestors 'self'", // Prevent embedding in other sites
                "base-uri 'self'", // Restrict base tag URLs
                "form-action 'self'" // Restrict form submissions
            ]),

            // Permissions Policy (formerly Feature Policy)
            'Permissions-Policy' => implode(', ', [
                'camera=()',
                'microphone=()',
                'geolocation=()',
                'payment=()',
                'usb=()',
                'magnetometer=()',
                'gyroscope=()',
                'accelerometer=()',
                'fullscreen=(self)'
            ]),

            // Remove server information
            'Server' => '', // Will be removed
            'X-Powered-By' => '', // Will be removed
        ];

        // Add HSTS header only for HTTPS
        if ($this->isHttps) {
            $this->headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
        }

        // Merge with custom settings
        if (isset($securitySettings['headers'])) {
            $this->headers = array_merge($this->headers, $securitySettings['headers']);
        }
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $response = $handler->handle($request);

        // Add security headers
        foreach ($this->headers as $name => $value) {
            if ($value === '') {
                // Remove header
                $response = $response->withoutHeader($name);
            } else {
                $response = $response->withHeader($name, $value);
            }
        }

        // Additional security measures for sensitive endpoints
        $uri = $request->getUri()->getPath();

        // Add extra security for admin/mark panel
        if (str_starts_with($uri, '/mark')) {
            $response = $this->addAdminSecurityHeaders($response);
        }

        // Add extra security for API endpoints
        if (str_starts_with($uri, '/api') || str_contains($uri, '/api/')) {
            $response = $this->addApiSecurityHeaders($response);
        }

        // Prevent caching of sensitive content
        if ($this->isSensitiveEndpoint($uri)) {
            $response = $this->addNoCacheHeaders($response);
        }

        return $response;
    }

    /**
     * Add extra security headers for admin panel
     */
    private function addAdminSecurityHeaders(ResponseInterface $response): ResponseInterface
    {
        return $response
            ->withHeader('X-Frame-Options', 'DENY') // Stricter for admin
            ->withHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->withHeader('Pragma', 'no-cache')
            ->withHeader('Expires', '0');
    }

    /**
     * Add extra security headers for API endpoints
     */
    private function addApiSecurityHeaders(ResponseInterface $response): ResponseInterface
    {
        return $response
            ->withHeader('X-Content-Type-Options', 'nosniff')
            ->withHeader('X-Frame-Options', 'DENY')
            ->withHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    }

    /**
     * Add no-cache headers for sensitive endpoints
     */
    private function addNoCacheHeaders(ResponseInterface $response): ResponseInterface
    {
        return $response
            ->withHeader('Cache-Control', 'no-cache, no-store, must-revalidate, private')
            ->withHeader('Pragma', 'no-cache')
            ->withHeader('Expires', '0');
    }

    /**
     * Check if endpoint contains sensitive information
     */
    private function isSensitiveEndpoint(string $uri): bool
    {
        $sensitivePatterns = [
            '/mark',
            '/auth',
            '/login',
            '/logout',
            '/api',
            '/admin'
        ];

        foreach ($sensitivePatterns as $pattern) {
            if (str_contains($uri, $pattern)) {
                return true;
            }
        }

        return false;
    }
}
