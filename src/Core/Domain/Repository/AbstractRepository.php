<?php

declare(strict_types = 1);

namespace App\Core\Domain\Repository;

use App\Core\Domain\ValueObject\EntityId;
use Cake\Database\Connection as CakeConnection;

/**
 * Abstract base repository with common functionality
 * 
 * @template T The entity type
 * @implements RepositoryInterface<T>
 */
abstract class AbstractRepository implements RepositoryInterface
{
    protected CakeConnection $connection;
    protected string $tableName;
    protected string $entityType;

    public function __construct(CakeConnection $connection, string $tableName, string $entityType)
    {
        $this->connection = $connection;
        $this->tableName = $tableName;
        $this->entityType = $entityType;
    }

    public function findById(EntityId $id): ?object
    {
        if (!$id->isForEntity($this->entityType)) {
            throw new \InvalidArgumentException(
                sprintf('Expected %s ID, got %s ID', $this->entityType, $id->getEntityType())
            );
        }

        $statement = $this->connection->execute(
            "SELECT * FROM {$this->tableName} WHERE id = :id",
            ['id' => $id->toRfc4122()]
        );

        $result = $statement->fetch('assoc');

        if (!$result) {
            return null;
        }

        return $this->mapToEntity($result);
    }

    public function exists(EntityId $id): bool
    {
        if (!$id->isForEntity($this->entityType)) {
            return false;
        }

        $statement = $this->connection->execute(
            "SELECT 1 FROM {$this->tableName} WHERE id = :id LIMIT 1",
            ['id' => $id->toRfc4122()]
        );

        return $statement->fetch() !== false;
    }

    public function count(): int
    {
        $statement = $this->connection->execute("SELECT COUNT(*) as count FROM {$this->tableName}");
        $result = $statement->fetch('assoc');

        return (int)$result['count'];
    }

    public function findAll(int $limit = 10, int $offset = 0): array
    {
        $statement = $this->connection->execute(
            "SELECT * FROM {$this->tableName} ORDER BY created_at DESC LIMIT :limit OFFSET :offset",
            [
                'limit' => $limit,
                'offset' => $offset,
            ]
        );

        $results = $statement->fetchAll('assoc');

        $entities = [];
        foreach ($results as $result) {
            $entities[] = $this->mapToEntity($result);
        }

        return $entities;
    }

    /**
     * Map database row to entity
     * Must be implemented by concrete repositories
     * 
     * @param array $row
     * @return T
     */
    abstract protected function mapToEntity(array $row): object;

    /**
     * Map entity to database row
     * Must be implemented by concrete repositories
     * 
     * @param T $entity
     * @return array
     */
    abstract protected function mapToRow(object $entity): array;

    /**
     * Get entity ID from entity
     * Must be implemented by concrete repositories
     * 
     * @param T $entity
     * @return EntityId
     */
    abstract protected function getEntityId(object $entity): EntityId;

    protected function getConnection(): CakeConnection
    {
        return $this->connection;
    }
}
