<?php

declare(strict_types = 1);

namespace App\Core\Domain\Repository;

use App\Core\Domain\ValueObject\EntityId;

/**
 * Universal Repository Interface
 * 
 * Provides common CRUD operations for all entities
 * Uses EntityId for universal ID handling
 * 
 * @template T The entity type
 */
interface RepositoryInterface
{
    /**
     * Find entity by ID
     * 
     * @param EntityId $id
     * @return T|null
     */
    public function findById(EntityId $id): ?object;

    /**
     * Save entity (insert or update)
     * 
     * @param T $entity
     * @return void
     */
    public function save(object $entity): void;

    /**
     * Delete entity
     * 
     * @param T $entity
     * @return void
     */
    public function delete(object $entity): void;

    /**
     * Count total entities
     * 
     * @return int
     */
    public function count(): int;

    /**
     * Find all entities with pagination
     * 
     * @param int $limit
     * @param int $offset
     * @return T[]
     */
    public function findAll(int $limit = 10, int $offset = 0): array;

    /**
     * Check if entity exists
     * 
     * @param EntityId $id
     * @return bool
     */
    public function exists(EntityId $id): bool;
}
