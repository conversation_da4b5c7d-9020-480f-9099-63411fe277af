<?php

declare(strict_types = 1);

namespace App\Core\Domain\ValueObject;

/**
 * Type-safe Mark ID
 *
 * Wrapper around EntityId for backward compatibility
 */
final class MarkId
{
    private EntityId $entityId;

    private function __construct(EntityId $entityId)
    {
        if (!$entityId->isForEntity('Mark')) {
            throw new \InvalidArgumentException('EntityId must be for Mark entity');
        }
        $this->entityId = $entityId;
    }

    public static function fromString(string $value): self
    {
        return new self(EntityId::forMark($value));
    }

    public static function generate(): self
    {
        return new self(EntityId::forMark());
    }

    public function toRfc4122(): string
    {
        return $this->entityId->toRfc4122();
    }

    public function equals(self $other): bool
    {
        return $this->entityId->equals($other->entityId);
    }

    public function __toString(): string
    {
        return $this->entityId->__toString();
    }

    /**
     * Convert to universal EntityId
     */
    public function toEntityId(): EntityId
    {
        return $this->entityId;
    }
}
