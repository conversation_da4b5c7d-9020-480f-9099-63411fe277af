<?php

declare(strict_types = 1);

namespace App\Core\Domain\ValueObject;

use Ramsey\Uuid\Uuid;

/**
 * Universal Entity ID Value Object
 * 
 * Replaces specific ID classes like UserId, MarkId, ArticleId
 * Provides type safety through generic typing and factory methods
 */
final class EntityId
{
    private function __construct(
        private readonly string $value,
        private readonly string $entityType,
    ) {
        if (!Uuid::isValid($this->value)) {
            throw new \InvalidArgumentException(sprintf('Invalid %s ID format: %s', $entityType, $value));
        }
    }

    /**
     * Create EntityId from string value
     */
    public static function fromString(string $value, string $entityType = 'Entity'): self
    {
        return new self($value, $entityType);
    }

    /**
     * Generate new EntityId
     */
    public static function generate(string $entityType = 'Entity'): self
    {
        return new self(Uuid::uuid4()->toString(), $entityType);
    }

    /**
     * Factory methods for specific entity types
     */
    public static function forUser(string $value = null): self
    {
        return $value ? self::fromString($value, 'User') : self::generate('User');
    }

    public static function forArticle(string $value = null): self
    {
        return $value ? self::fromString($value, 'Article') : self::generate('Article');
    }

    public static function forMark(string $value = null): self
    {
        return $value ? self::fromString($value, 'Mark') : self::generate('Mark');
    }

    /**
     * Get RFC4122 formatted UUID string
     */
    public function toRfc4122(): string
    {
        return $this->value;
    }

    /**
     * Get entity type
     */
    public function getEntityType(): string
    {
        return $this->entityType;
    }

    /**
     * Check equality with another EntityId
     */
    public function equals(self $other): bool
    {
        return $this->value === $other->value && $this->entityType === $other->entityType;
    }

    /**
     * String representation
     */
    public function __toString(): string
    {
        return $this->value;
    }

    /**
     * Check if this ID is for specific entity type
     */
    public function isForEntity(string $entityType): bool
    {
        return $this->entityType === $entityType;
    }
}
