<?php

declare(strict_types = 1);

namespace App\Core\Domain\ValueObject;

/**
 * Type-safe Article ID
 *
 * Wrapper around EntityId for backward compatibility
 */
final class ArticleId
{
    private EntityId $entityId;

    private function __construct(EntityId $entityId)
    {
        if (!$entityId->isForEntity('Article')) {
            throw new \InvalidArgumentException('EntityId must be for Article entity');
        }
        $this->entityId = $entityId;
    }

    public static function fromString(string $value): self
    {
        return new self(EntityId::forArticle($value));
    }

    public static function generate(): self
    {
        return new self(EntityId::forArticle());
    }

    public function toRfc4122(): string
    {
        return $this->entityId->toRfc4122();
    }

    public function equals(self $other): bool
    {
        return $this->entityId->equals($other->entityId);
    }

    public function __toString(): string
    {
        return $this->entityId->__toString();
    }

    /**
     * Convert to universal EntityId
     */
    public function toEntityId(): EntityId
    {
        return $this->entityId;
    }
}
