<?php

declare(strict_types = 1);

namespace App\Core\Domain\Enum;

enum Currency: string
{
    case EUR = 'EUR';
    case USD = 'USD';
    case GBP = 'GBP';
    case CZK = 'CZK';
    case PLN = 'PLN';

    public function getLabel(): string
    {
        return match($this) {
            self::EUR => 'Euro (€)',
            self::USD => 'US Dollar ($)',
            self::GBP => 'British Pound (£)',
            self::CZK => 'Czech Koruna (Kč)',
            self::PLN => 'Polish Złoty (zł)',
        };
    }

    public function getSymbol(): string
    {
        return match($this) {
            self::EUR => '€',
            self::USD => '$',
            self::GBP => '£',
            self::CZK => 'Kč',
            self::PLN => 'zł',
        };
    }
}