<?php

declare(strict_types = 1);

namespace App\Core\Domain\Enum;

enum ProductType: string
{
    case ARTICLE = 'article';
    case PHYSICAL = 'physical';
    case DIGITAL = 'digital';
    case SERVICE = 'service';
    case BUNDLE = 'bundle';

    public function getLabel(): string
    {
        return match($this) {
            self::ARTICLE => 'Article',
            self::PHYSICAL => 'Physical Product',
            self::DIGITAL => 'Digital Product',
            self::SERVICE => 'Service',
            self::BUNDLE => 'Bundle',
        };
    }

    public function getPrefix(): string
    {
        return match($this) {
            self::ARTICLE => 'ART',
            self::PHYSICAL => 'PHY',
            self::DIGITAL => 'DIG',
            self::SERVICE => 'SRV',
            self::BUNDLE => 'BUN',
        };
    }
}