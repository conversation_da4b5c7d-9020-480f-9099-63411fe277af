{"version": 1, "defects": {"App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageAction": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateAction": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#too-short": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#too-long": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#empty-values": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#empty-request-body": 8, "App\\Test\\TestCase\\User\\Delete\\UserDeleteActionTest::testUserSubmitDeleteAction": 8, "App\\Test\\TestCase\\User\\List\\ApiUserFetchListActionTest::testUserFetchListAction": 8, "App\\Test\\TestCase\\User\\List\\UserFetchListActionTest::testUserFetchList": 8, "App\\Test\\TestCase\\User\\Read\\UserReadPageActionTest::testUserReadPageAction": 8, "App\\Test\\TestCase\\User\\Read\\UserReadPageActionTest::testUserReadPageActionNotFound": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateAction": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateInvalid#0": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateInvalid#1": 8, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testCanBeCreated": 8, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayReturnsCorrectData": 8}, "times": {"App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageAction": 0.008, "App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageRedirectAction": 0.001, "App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageActionNotFound": 0.002, "App\\Test\\TestCase\\User\\List\\UserListPageActionTest::testUserListPageAction": 0.001, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testCanBeCreated": 0.002, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayReturnsCorrectData": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindLatestReturnsLimitedNumberOfArticles": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindByIdReturnsCorrectArticle": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindBySlugReturnsCorrectArticle": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindBySlugReturnsNullForNonExistentSlug": 0, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayUsesPublishedAtForDateWhenAvailable": 0}}